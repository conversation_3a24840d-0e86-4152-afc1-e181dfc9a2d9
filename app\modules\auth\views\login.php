<?php
include BASE_PATH . '/resources/views/components/header/public-header.php';
?>

<!-- Login Form -->
<div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-12">
    <div class="max-w-md mx-auto px-4">
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-3xl font-bold text-gray-800 mb-2">🔐 লগইন করুন</h1>
            <p class="text-gray-600">আপনার অ্যাকাউন্টে প্রবেশ করুন</p>
        </div>

        <!-- Login Form -->
        <div class="bg-white rounded-lg shadow-lg p-6">
            <form id="loginForm" class="space-y-4">
                <!-- Email or Username -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">ইমেইল বা ইউজারনেম *</label>
                    <input type="text" name="email_or_username" required 
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                           placeholder="আপনার ইমেইল বা ইউজারনেম">
                    <div class="error-message text-red-500 text-xs mt-1 hidden"></div>
                </div>

                <!-- Password -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">পাসওয়ার্ড *</label>
                    <div class="relative">
                        <input type="password" name="password" required 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                               placeholder="আপনার পাসওয়ার্ড">
                        <button type="button" class="absolute right-3 top-2 text-gray-500 hover:text-gray-700" 
                                onclick="togglePassword()">
                            <i class="fas fa-eye" id="passwordToggleIcon"></i>
                        </button>
                    </div>
                    <div class="error-message text-red-500 text-xs mt-1 hidden"></div>
                </div>

                <!-- Remember Me -->
                <div class="flex items-center justify-between">
                    <label class="flex items-center">
                        <input type="checkbox" name="remember_me" value="1" 
                               class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                        <span class="ml-2 text-sm text-gray-600">আমাকে মনে রাখুন</span>
                    </label>
                    <a href="/jobspace/forgot-password" class="text-sm text-blue-600 hover:text-blue-800">
                        পাসওয়ার্ড ভুলে গেছেন?
                    </a>
                </div>

                <!-- Submit Button -->
                <button type="submit" 
                        class="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 font-medium">
                    লগইন করুন
                </button>

                <!-- Error Message -->
                <div id="loginError" class="text-red-500 text-sm text-center hidden"></div>
            </form>

            <!-- Social Login (if enabled) -->
            <div class="mt-6">
                <div class="relative">
                    <div class="absolute inset-0 flex items-center">
                        <div class="w-full border-t border-gray-300"></div>
                    </div>
                    <div class="relative flex justify-center text-sm">
                        <span class="px-2 bg-white text-gray-500">অথবা</span>
                    </div>
                </div>

                <div class="mt-6 grid grid-cols-2 gap-3">
                    <button class="w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                        <i class="fab fa-google text-red-500 mr-2"></i>
                        Google
                    </button>
                    <button class="w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                        <i class="fab fa-facebook text-blue-600 mr-2"></i>
                        Facebook
                    </button>
                </div>
            </div>

            <!-- Register Link -->
            <div class="text-center mt-6 pt-6 border-t border-gray-200">
                <p class="text-gray-600">
                    নতুন ব্যবহারকারী? 
                    <a href="/jobspace/register" class="text-blue-600 hover:text-blue-800 font-medium">রেজিস্টার করুন</a>
                </p>
            </div>
        </div>

        <!-- Security Notice -->
        <div class="mt-6 text-center">
            <p class="text-xs text-gray-500">
                <i class="fas fa-shield-alt mr-1"></i>
                আপনার তথ্য সুরক্ষিত এবং এনক্রিপ্টেড
            </p>
        </div>
    </div>
</div>

<script>
// Toggle password visibility
function togglePassword() {
    const passwordInput = document.querySelector('input[name="password"]');
    const toggleIcon = document.getElementById('passwordToggleIcon');
    
    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        toggleIcon.classList.remove('fa-eye');
        toggleIcon.classList.add('fa-eye-slash');
    } else {
        passwordInput.type = 'password';
        toggleIcon.classList.remove('fa-eye-slash');
        toggleIcon.classList.add('fa-eye');
    }
}

// Handle login form submission
document.getElementById('loginForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    // Clear previous errors
    document.querySelectorAll('.error-message').forEach(el => {
        el.classList.add('hidden');
        el.textContent = '';
    });
    document.getElementById('loginError').classList.add('hidden');
    
    const formData = new FormData(this);
    const data = Object.fromEntries(formData);
    
    // Show loading state
    const submitButton = this.querySelector('button[type="submit"]');
    const originalText = submitButton.textContent;
    submitButton.textContent = 'লগইন হচ্ছে...';
    submitButton.disabled = true;
    
    try {
        const response = await fetch('/jobspace/login/process', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        });
        
        const result = await response.json();
        
        if (result.success) {
            // Redirect to appropriate dashboard
            window.location.href = result.redirect;
        } else {
            // Show error message
            if (result.errors) {
                // Show field-specific errors
                Object.keys(result.errors).forEach(field => {
                    const errorDiv = document.querySelector(`[name="${field}"]`).nextElementSibling;
                    if (errorDiv && errorDiv.classList.contains('error-message')) {
                        errorDiv.textContent = result.errors[field];
                        errorDiv.classList.remove('hidden');
                    }
                });
            } else {
                // Show general error
                const errorDiv = document.getElementById('loginError');
                errorDiv.textContent = result.message || 'লগইন করতে সমস্যা হয়েছে';
                errorDiv.classList.remove('hidden');
            }
        }
    } catch (error) {
        const errorDiv = document.getElementById('loginError');
        errorDiv.textContent = 'একটি ত্রুটি ঘটেছে। আবার চেষ্টা করুন।';
        errorDiv.classList.remove('hidden');
    } finally {
        // Reset button state
        submitButton.textContent = originalText;
        submitButton.disabled = false;
    }
});
</script>

<?php include BASE_PATH . '/resources/views/components/footer/public-footer.php'; ?>

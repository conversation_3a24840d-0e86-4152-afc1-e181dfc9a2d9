<?php

namespace App\Modules\Public\Controllers;

use App\Modules\Public\Services\PageService;
use App\Modules\Public\Services\NavigationService;
use App\Modules\Public\Services\CommunityService;
use App\Modules\Public\Services\HelpService;
use App\Modules\Public\Models\PageModel;
use App\Modules\Public\Models\CommunityModel;
use App\Modules\Public\Models\HelpModel;

/**
 * Public Controller
 * Handles all public pages
 */
class PublicController
{
    private PageService $pageService;
    private NavigationService $navigationService;
    private CommunityService $communityService;
    private HelpService $helpService;

    public function __construct()
    {
        $this->pageService = new PageService();
        $this->navigationService = new NavigationService();
        $this->communityService = new CommunityService();
        $this->helpService = new HelpService();
    }

    public function home()
    {
        $data = [
            'stats' => PageModel::getPlatformStats(),
            'features' => PageModel::getPlatformFeatures()
        ];

        return $this->pageService->renderPage('home', $data);
    }

    public function about()
    {
        return $this->pageService->renderPage('about');
    }

    public function contact()
    {
        $data = [];

        // Handle contact form submission
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $formData = [
                'name' => $_POST['name'] ?? '',
                'email' => $_POST['email'] ?? '',
                'message' => $_POST['message'] ?? ''
            ];

            $result = $this->pageService->processContactForm($formData);
            $data['form_result'] = $result;
        }

        return $this->pageService->renderPage('contact', $data);
    }

    public function terms()
    {
        return $this->pageService->renderPage('terms');
    }

    public function community()
    {
        $data = [
            'community_stats' => $this->communityService->getCommunityStats(),
            'recent_activities' => $this->communityService->getRecentActivities()
        ];

        return $this->pageService->renderPage('community', $data);
    }

    public function help()
    {
        $data = [
            'faq_items' => $this->helpService->getFaqItems(),
            'help_categories' => $this->helpService->getHelpCategories()
        ];

        return $this->pageService->renderPage('help', $data);
    }

    public function privacy()
    {
        return $this->pageService->renderPage('privacy');
    }
}

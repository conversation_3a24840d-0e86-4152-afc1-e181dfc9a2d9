<?php

namespace App\Modules\Dashboard\Services;

/**
 * Dashboard Service
 * Core dashboard functionality
 */
class DashboardService
{
    /**
     * Get recent activities by role
     */
    public function getRecentActivities(string $role, ?int $userId = null): array
    {
        // Mock data - in real application, this would fetch from database
        $activities = [
            [
                'id' => 1,
                'type' => 'login',
                'title' => 'সিস্টেমে লগইন',
                'description' => 'সফলভাবে লগইন হয়েছেন',
                'icon' => 'fas fa-sign-in-alt',
                'color' => 'green',
                'time' => '৫ মিনিট আগে',
                'timestamp' => time() - 300
            ],
            [
                'id' => 2,
                'type' => 'profile_update',
                'title' => 'প্রোফাইল আপডেট',
                'description' => 'প্রোফাইল তথ্য আপডেট করা হয়েছে',
                'icon' => 'fas fa-user-edit',
                'color' => 'blue',
                'time' => '২ ঘন্টা আগে',
                'timestamp' => time() - 7200
            ]
        ];

        // Add role-specific activities
        switch ($role) {
            case 'admin':
                $activities = array_merge($activities, [
                    [
                        'id' => 3,
                        'type' => 'user_management',
                        'title' => 'নতুন ব্যবহারকারী',
                        'description' => '৫ জন নতুন ব্যবহারকারী রেজিস্টার হয়েছেন',
                        'icon' => 'fas fa-users',
                        'color' => 'purple',
                        'time' => '১ ঘন্টা আগে',
                        'timestamp' => time() - 3600
                    ]
                ]);
                break;
                
            case 'creator':
                $activities = array_merge($activities, [
                    [
                        'id' => 4,
                        'type' => 'content_created',
                        'title' => 'নতুন কুইজ তৈরি',
                        'description' => 'গণিত বিষয়ে একটি কুইজ তৈরি করেছেন',
                        'icon' => 'fas fa-brain',
                        'color' => 'orange',
                        'time' => '৩ ঘন্টা আগে',
                        'timestamp' => time() - 10800
                    ]
                ]);
                break;
        }

        return array_slice($activities, 0, 10);
    }

    /**
     * Get system health (for admin)
     */
    public function getSystemHealth(): array
    {
        return [
            'server_status' => 'healthy',
            'database_status' => 'healthy',
            'cache_status' => 'healthy',
            'storage_usage' => 65,
            'memory_usage' => 45,
            'cpu_usage' => 30,
            'active_users' => 1250,
            'response_time' => 120 // ms
        ];
    }

    /**
     * Get earnings info
     */
    public function getEarnings(int $userId): array
    {
        return [
            'total_earnings' => 15000,
            'this_month' => 3500,
            'pending' => 500,
            'withdrawn' => 11000,
            'currency' => 'BDT',
            'recent_transactions' => [
                [
                    'type' => 'quiz_reward',
                    'amount' => 50,
                    'description' => 'কুইজ পুরস্কার',
                    'date' => '২০২৪-০৮-১৫',
                    'status' => 'completed'
                ],
                [
                    'type' => 'withdrawal',
                    'amount' => -1000,
                    'description' => 'টাকা উত্তোলন',
                    'date' => '২০২৪-০৮-১৪',
                    'status' => 'completed'
                ]
            ]
        ];
    }

    /**
     * Get campaigns (for business)
     */
    public function getCampaigns(int $userId): array
    {
        return [
            [
                'id' => 1,
                'name' => 'গ্রীষ্মকালীন অফার',
                'status' => 'active',
                'budget' => 5000,
                'spent' => 2500,
                'impressions' => 15000,
                'clicks' => 450,
                'conversions' => 25,
                'start_date' => '২০২৪-০৮-০১',
                'end_date' => '২০২৪-০৮-৩১'
            ],
            [
                'id' => 2,
                'name' => 'নতুন পণ্য প্রচার',
                'status' => 'paused',
                'budget' => 3000,
                'spent' => 1200,
                'impressions' => 8000,
                'clicks' => 200,
                'conversions' => 12,
                'start_date' => '২০২৪-০৮-১০',
                'end_date' => '২০২৪-০৮-২৫'
            ]
        ];
    }

    /**
     * Get achievements (for user)
     */
    public function getAchievements(int $userId): array
    {
        return [
            [
                'id' => 1,
                'title' => 'কুইজ মাস্টার',
                'description' => '১০০টি কুইজ সম্পন্ন করেছেন',
                'icon' => 'fas fa-trophy',
                'color' => 'gold',
                'earned_date' => '২০২৪-০৮-১০',
                'points' => 500
            ],
            [
                'id' => 2,
                'title' => 'সোশ্যাল বাটারফ্লাই',
                'description' => '৫০টি পোস্ট শেয়ার করেছেন',
                'icon' => 'fas fa-share',
                'color' => 'blue',
                'earned_date' => '২০২৪-০৮-০৫',
                'points' => 200
            ],
            [
                'id' => 3,
                'title' => 'শপিং এক্সপার্ট',
                'description' => '২০টি পণ্য কিনেছেন',
                'icon' => 'fas fa-shopping-cart',
                'color' => 'green',
                'earned_date' => '২০২৪-০৭-২৮',
                'points' => 300
            ]
        ];
    }

    /**
     * Get wallet info (for user)
     */
    public function getWalletInfo(int $userId): array
    {
        return [
            'balance' => 2500,
            'pending' => 150,
            'total_earned' => 8000,
            'total_spent' => 5350,
            'currency' => 'BDT',
            'recent_transactions' => [
                [
                    'id' => 1,
                    'type' => 'earned',
                    'amount' => 100,
                    'description' => 'কুইজ জয়ের পুরস্কার',
                    'date' => '২০২৪-০৮-১৫ ১০:৩০',
                    'status' => 'completed'
                ],
                [
                    'id' => 2,
                    'type' => 'spent',
                    'amount' => -50,
                    'description' => 'প্রিমিয়াম কুইজ ফি',
                    'date' => '২০২৪-০৮-১৪ ১৫:২০',
                    'status' => 'completed'
                ],
                [
                    'id' => 3,
                    'type' => 'earned',
                    'amount' => 25,
                    'description' => 'রেফারেল বোনাস',
                    'date' => '২০২৪-০৮-১৩ ০৯:১৫',
                    'status' => 'completed'
                ]
            ]
        ];
    }

    /**
     * Get quick actions by role
     */
    public function getQuickActions(string $role): array
    {
        $actions = [];

        switch ($role) {
            case 'admin':
                $actions = [
                    ['title' => 'ব্যবহারকারী পরিচালনা', 'url' => '/dashboard/admin/users', 'icon' => 'fas fa-users'],
                    ['title' => 'সিস্টেম সেটিংস', 'url' => '/dashboard/admin/settings', 'icon' => 'fas fa-cog'],
                    ['title' => 'রিপোর্ট দেখুন', 'url' => '/dashboard/admin/reports', 'icon' => 'fas fa-chart-bar'],
                    ['title' => 'মডিউল পরিচালনা', 'url' => '/dashboard/admin/modules', 'icon' => 'fas fa-puzzle-piece']
                ];
                break;
                
            case 'creator':
                $actions = [
                    ['title' => 'নতুন কুইজ তৈরি', 'url' => '/quiz/create', 'icon' => 'fas fa-brain'],
                    ['title' => 'পণ্য যোগ করুন', 'url' => '/ecommerce/products/create', 'icon' => 'fas fa-plus'],
                    ['title' => 'জব পোস্ট করুন', 'url' => '/freelance/jobs/create', 'icon' => 'fas fa-briefcase'],
                    ['title' => 'বিজ্ঞাপন তৈরি', 'url' => '/ads/create', 'icon' => 'fas fa-ad']
                ];
                break;
                
            case 'business':
                $actions = [
                    ['title' => 'নতুন ক্যাম্পেইন', 'url' => '/ads/campaigns/create', 'icon' => 'fas fa-bullhorn'],
                    ['title' => 'বিজ্ঞাপন তৈরি', 'url' => '/ads/create', 'icon' => 'fas fa-ad'],
                    ['title' => 'পারফরম্যান্স দেখুন', 'url' => '/dashboard/business/analytics', 'icon' => 'fas fa-chart-line'],
                    ['title' => 'বাজেট পরিচালনা', 'url' => '/dashboard/business/budget', 'icon' => 'fas fa-wallet']
                ];
                break;
                
            case 'user':
                $actions = [
                    ['title' => 'কুইজ খেলুন', 'url' => '/quiz', 'icon' => 'fas fa-play'],
                    ['title' => 'কেনাকাটা করুন', 'url' => '/ecommerce', 'icon' => 'fas fa-shopping-cart'],
                    ['title' => 'জব খুঁজুন', 'url' => '/freelance/jobs', 'icon' => 'fas fa-search'],
                    ['title' => 'প্রোফাইল আপডেট', 'url' => '/profile', 'icon' => 'fas fa-user-edit']
                ];
                break;
        }

        return $actions;
    }
}

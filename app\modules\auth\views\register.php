<?php
include BASE_PATH . '/resources/views/components/header/public-header.php';
?>

<!-- Registration Step 1 -->
<div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-12">
    <div class="max-w-md mx-auto px-4">
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-3xl font-bold text-gray-800 mb-2">🚀 যোগ দিন JobSpace এ</h1>
            <p class="text-gray-600">আপনার যাত্রা শুরু করুন মাত্র কয়েক ধাপে</p>
        </div>

        <!-- Progress Bar -->
        <div class="mb-8">
            <div class="flex items-center justify-between mb-2">
                <span class="text-sm font-medium text-blue-600">ধাপ ১</span>
                <span class="text-sm text-gray-500">২ এর মধ্যে</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
                <div class="bg-blue-600 h-2 rounded-full" style="width: 50%"></div>
            </div>
        </div>

        <!-- Registration Form -->
        <div class="bg-white rounded-lg shadow-lg p-6">
            <form id="registrationForm" class="space-y-4">
                <!-- Name Fields -->
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">নাম *</label>
                        <input type="text" name="first_name" required 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                               placeholder="প্রথম নাম">
                        <div class="error-message text-red-500 text-xs mt-1 hidden"></div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">পদবি *</label>
                        <input type="text" name="last_name" required 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                               placeholder="শেষ নাম">
                        <div class="error-message text-red-500 text-xs mt-1 hidden"></div>
                    </div>
                </div>

                <!-- Email -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">ইমেইল ঠিকানা *</label>
                    <input type="email" name="email" required 
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                           placeholder="<EMAIL>">
                    <div class="error-message text-red-500 text-xs mt-1 hidden"></div>
                </div>

                <!-- Phone -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">মোবাইল নম্বর *</label>
                    <input type="tel" name="phone" required 
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                           placeholder="01XXXXXXXXX">
                    <div class="error-message text-red-500 text-xs mt-1 hidden"></div>
                </div>

                <!-- Date of Birth -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">জন্ম তারিখ *</label>
                    <input type="date" name="date_of_birth" required 
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <div class="error-message text-red-500 text-xs mt-1 hidden"></div>
                </div>

                <!-- Gender -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">লিঙ্গ *</label>
                    <select name="gender" required 
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">নির্বাচন করুন</option>
                        <option value="male">পুরুষ</option>
                        <option value="female">মহিলা</option>
                        <option value="other">অন্যান্য</option>
                    </select>
                    <div class="error-message text-red-500 text-xs mt-1 hidden"></div>
                </div>

                <!-- Password -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">পাসওয়ার্ড *</label>
                    <input type="password" name="password" required 
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                           placeholder="কমপক্ষে ৮ অক্ষর">
                    <div class="error-message text-red-500 text-xs mt-1 hidden"></div>
                </div>

                <!-- Confirm Password -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">পাসওয়ার্ড নিশ্চিত করুন *</label>
                    <input type="password" name="confirm_password" required 
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                           placeholder="পাসওয়ার্ড পুনরায় লিখুন">
                    <div class="error-message text-red-500 text-xs mt-1 hidden"></div>
                </div>

                <!-- Submit Button -->
                <button type="submit" 
                        class="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 font-medium">
                    পরবর্তী ধাপ →
                </button>
            </form>

            <!-- Login Link -->
            <div class="text-center mt-6 pt-6 border-t border-gray-200">
                <p class="text-gray-600">
                    ইতিমধ্যে অ্যাকাউন্ট আছে? 
                    <a href="/jobspace/login" class="text-blue-600 hover:text-blue-800 font-medium">লগইন করুন</a>
                </p>
            </div>
        </div>
    </div>
</div>

<script>
document.getElementById('registrationForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const data = Object.fromEntries(formData);
    
    try {
        const response = await fetch('/jobspace/register/step1', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        });
        
        const result = await response.json();
        
        if (result.success) {
            window.location.href = '/jobspace/register/step2';
        } else {
            // Show errors
            Object.keys(result.errors || {}).forEach(field => {
                const errorDiv = document.querySelector(`[name="${field}"]`).nextElementSibling;
                if (errorDiv && errorDiv.classList.contains('error-message')) {
                    errorDiv.textContent = result.errors[field];
                    errorDiv.classList.remove('hidden');
                }
            });
        }
    } catch (error) {
        alert('একটি ত্রুটি ঘটেছে। আবার চেষ্টা করুন।');
    }
});
</script>

<?php include BASE_PATH . '/resources/views/components/footer/public-footer.php'; ?>

<?php

namespace App\Modules\Auth\Services;

use App\Modules\Auth\Models\UserModel;
use App\Modules\Auth\Models\LoginAttemptModel;

/**
 * Login Service
 * Handles user login and session management
 */
class LoginService
{
    private AuthService $authService;

    public function __construct()
    {
        $this->authService = new AuthService();
    }

    /**
     * Attempt user login
     */
    public function attemptLogin(array $credentials): array
    {
        $identifier = $credentials['email_or_username'];
        $password = $credentials['password'];
        $rememberMe = $credentials['remember_me'] ?? false;

        // Check login attempts
        if ($this->isAccountLocked($identifier)) {
            return [
                'success' => false,
                'message' => 'অ্যাকাউন্ট সাময়িকভাবে লক হয়েছে। পরে আবার চেষ্টা করুন।'
            ];
        }

        // Find user
        $user = UserModel::findByEmailOrUsername($identifier);

        if (!$user) {
            $this->recordFailedAttempt($identifier);
            return [
                'success' => false,
                'message' => 'ইমেইল/ইউজারনেম বা পাসওয়ার্ড ভুল'
            ];
        }

        // Check if account is active
        if (!$user['is_active']) {
            return [
                'success' => false,
                'message' => 'আপনার অ্যাকাউন্ট নিষ্ক্রিয় করা হয়েছে'
            ];
        }

        // Verify password
        if (!$this->authService->verifyPassword($password, $user['password_hash'])) {
            $this->recordFailedAttempt($identifier);
            return [
                'success' => false,
                'message' => 'ইমেইল/ইউজারনেম বা পাসওয়ার্ড ভুল'
            ];
        }

        // Login successful
        $this->loginUser($user, $rememberMe);
        $this->clearFailedAttempts($identifier);

        return [
            'success' => true,
            'message' => 'সফলভাবে লগইন হয়েছে',
            'redirect_url' => $this->authService->getRedirectUrl($user['role'])
        ];
    }

    /**
     * Login user and create session
     */
    public function loginUser(array $user, bool $rememberMe = false): void
    {
        // Start session if not started
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }

        // Set session data
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['user_role'] = $user['role'];
        $_SESSION['user_email'] = $user['email'];
        $_SESSION['login_time'] = time();

        // Set remember me cookie if requested
        if ($rememberMe) {
            $this->setRememberMeCookie($user['id']);
        }

        // Update last login time
        UserModel::updateLastLogin($user['id']);

        // Log login activity
        $this->logLoginActivity($user);
    }

    /**
     * Logout user
     */
    public function logout(): void
    {
        // Start session if not started
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }

        // Log logout activity
        if (isset($_SESSION['user_id'])) {
            $this->logLogoutActivity($_SESSION['user_id']);
        }

        // Clear session
        session_unset();
        session_destroy();

        // Clear remember me cookie
        $this->clearRememberMeCookie();
    }

    /**
     * Check if account is locked due to failed attempts
     */
    private function isAccountLocked(string $identifier): bool
    {
        $config = $this->authService->getConfig('login_attempts');
        $attempts = LoginAttemptModel::getRecentAttempts($identifier, $config['reset_time']);

        return count($attempts) >= $config['max_attempts'];
    }

    /**
     * Record failed login attempt
     */
    private function recordFailedAttempt(string $identifier): void
    {
        LoginAttemptModel::record($identifier, false);
    }

    /**
     * Clear failed login attempts
     */
    private function clearFailedAttempts(string $identifier): void
    {
        LoginAttemptModel::clearAttempts($identifier);
    }

    /**
     * Set remember me cookie
     */
    private function setRememberMeCookie(int $userId): void
    {
        $token = bin2hex(random_bytes(32));
        $expiry = time() + $this->authService->getConfig('remember_me_lifetime');

        // Store token in database (in real application)
        // For now, just set cookie
        setcookie('remember_token', $token, $expiry, '/', '', true, true);
    }

    /**
     * Clear remember me cookie
     */
    private function clearRememberMeCookie(): void
    {
        setcookie('remember_token', '', time() - 3600, '/', '', true, true);
    }

    /**
     * Check remember me token
     */
    public function checkRememberMe(): ?array
    {
        if (!isset($_COOKIE['remember_token'])) {
            return null;
        }

        // In real application, verify token from database
        // For now, return null
        return null;
    }

    /**
     * Log login activity
     */
    private function logLoginActivity(array $user): void
    {
        $data = [
            'user_id' => $user['id'],
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            'login_time' => date('Y-m-d H:i:s')
        ];

        // In real application, store in database
        error_log("User login: {$user['email']} from {$data['ip_address']}");
    }

    /**
     * Log logout activity
     */
    private function logLogoutActivity(int $userId): void
    {
        // In real application, store in database
        error_log("User logout: ID {$userId}");
    }

    /**
     * Get login attempts for user
     */
    public function getLoginAttempts(string $identifier = null): array
    {
        if (!$identifier) {
            return [];
        }

        return LoginAttemptModel::getRecentAttempts($identifier, 3600); // Last hour
    }

    /**
     * Check if user session is valid
     */
    public function isSessionValid(): bool
    {
        if (!isset($_SESSION['user_id']) || !isset($_SESSION['login_time'])) {
            return false;
        }

        $sessionLifetime = $this->authService->getConfig('session_lifetime');
        $loginTime = $_SESSION['login_time'];

        // Check if session has expired
        if (time() - $loginTime > $sessionLifetime) {
            return false;
        }

        return true;
    }

    /**
     * Refresh session
     */
    public function refreshSession(): void
    {
        $_SESSION['login_time'] = time();
    }

    /**
     * Get current session info
     */
    public function getSessionInfo(): array
    {
        if (!$this->authService->isLoggedIn()) {
            return [];
        }

        return [
            'user_id' => $_SESSION['user_id'] ?? null,
            'user_role' => $_SESSION['user_role'] ?? null,
            'user_email' => $_SESSION['user_email'] ?? null,
            'login_time' => $_SESSION['login_time'] ?? null,
            'session_expires' => ($_SESSION['login_time'] ?? 0) + $this->authService->getConfig('session_lifetime')
        ];
    }
}

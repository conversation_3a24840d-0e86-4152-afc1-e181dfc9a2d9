<?php
// Sample notifications - in real app, fetch from database
$notifications = [
    [
        'id' => 1,
        'type' => 'quiz',
        'title' => 'Quiz Completed',
        'message' => 'You earned 50 points from Math Quiz!',
        'time' => '2 minutes ago',
        'read' => false,
        'icon' => 'fas fa-brain',
        'color' => 'text-blue-500'
    ],
    [
        'id' => 2,
        'type' => 'payment',
        'title' => 'Payment Received',
        'message' => '₹500 added to your wallet',
        'time' => '1 hour ago',
        'read' => false,
        'icon' => 'fas fa-dollar-sign',
        'color' => 'text-green-500'
    ],
    [
        'id' => 3,
        'type' => 'social',
        'title' => 'New Follower',
        'message' => '<PERSON> started following you',
        'time' => '3 hours ago',
        'read' => true,
        'icon' => 'fas fa-user-plus',
        'color' => 'text-purple-500'
    ],
    [
        'id' => 4,
        'type' => 'job',
        'title' => 'Job Application',
        'message' => 'New application for your Web Developer job',
        'time' => '1 day ago',
        'read' => true,
        'icon' => 'fas fa-briefcase',
        'color' => 'text-orange-500'
    ]
];

$unreadCount = count(array_filter($notifications, fn($n) => !$n['read']));
?>

<div class="notification-dropdown absolute right-0 top-full mt-2 w-80 bg-white rounded-lg shadow-lg border hidden z-50" id="notificationDropdown">
    <!-- Header -->
    <div class="px-4 py-3 border-b border-gray-200">
        <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-900">Notifications</h3>
            <?php if ($unreadCount > 0): ?>
                <span class="bg-blue-500 text-white text-xs px-2 py-1 rounded-full">
                    <?= $unreadCount ?> new
                </span>
            <?php endif; ?>
        </div>
        
        <?php if ($unreadCount > 0): ?>
            <button class="text-sm text-blue-600 hover:text-blue-700 mt-1" onclick="markAllAsRead()">
                Mark all as read
            </button>
        <?php endif; ?>
    </div>
    
    <!-- Notifications List -->
    <div class="max-h-96 overflow-y-auto">
        <?php if (empty($notifications)): ?>
            <div class="px-4 py-8 text-center text-gray-500">
                <i class="fas fa-bell-slash text-3xl mb-2"></i>
                <p>No notifications yet</p>
            </div>
        <?php else: ?>
            <?php foreach ($notifications as $notification): ?>
                <div class="notification-item px-4 py-3 hover:bg-gray-50 border-b border-gray-100 cursor-pointer <?= !$notification['read'] ? 'bg-blue-50' : '' ?>"
                     data-id="<?= $notification['id'] ?>"
                     onclick="markAsRead(<?= $notification['id'] ?>)">
                    <div class="flex items-start space-x-3">
                        <!-- Icon -->
                        <div class="flex-shrink-0 mt-1">
                            <div class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center">
                                <i class="<?= $notification['icon'] ?> <?= $notification['color'] ?> text-sm"></i>
                            </div>
                        </div>
                        
                        <!-- Content -->
                        <div class="flex-1 min-w-0">
                            <div class="flex items-center justify-between">
                                <p class="text-sm font-medium text-gray-900 truncate">
                                    <?= htmlspecialchars($notification['title']) ?>
                                </p>
                                <?php if (!$notification['read']): ?>
                                    <div class="w-2 h-2 bg-blue-500 rounded-full ml-2"></div>
                                <?php endif; ?>
                            </div>
                            <p class="text-sm text-gray-600 mt-1">
                                <?= htmlspecialchars($notification['message']) ?>
                            </p>
                            <p class="text-xs text-gray-400 mt-1">
                                <?= htmlspecialchars($notification['time']) ?>
                            </p>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>
    
    <!-- Footer -->
    <div class="px-4 py-3 border-t border-gray-200 bg-gray-50 rounded-b-lg">
        <a href="<?= Router::url('/notifications') ?>" 
           class="text-sm text-blue-600 hover:text-blue-700 font-medium">
            View all notifications
        </a>
    </div>
</div>

<script>
function toggleNotifications() {
    const dropdown = document.getElementById('notificationDropdown');
    dropdown.classList.toggle('hidden');
    
    // Close other dropdowns
    const userMenu = document.getElementById('userMenuDropdown');
    if (userMenu && !userMenu.classList.contains('hidden')) {
        userMenu.classList.add('hidden');
    }
}

function markAsRead(notificationId) {
    // Send AJAX request to mark as read
    fetch('<?= Router::url('/api/notifications/mark-read') ?>', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
        },
        body: JSON.stringify({ id: notificationId })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update UI
            const item = document.querySelector(`[data-id="${notificationId}"]`);
            if (item) {
                item.classList.remove('bg-blue-50');
                const dot = item.querySelector('.w-2.h-2.bg-blue-500');
                if (dot) dot.remove();
            }
            
            // Update notification count
            updateNotificationCount();
        }
    })
    .catch(error => console.error('Error:', error));
}

function markAllAsRead() {
    // Send AJAX request to mark all as read
    fetch('<?= Router::url('/api/notifications/mark-all-read') ?>', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update UI
            const items = document.querySelectorAll('.notification-item');
            items.forEach(item => {
                item.classList.remove('bg-blue-50');
                const dot = item.querySelector('.w-2.h-2.bg-blue-500');
                if (dot) dot.remove();
            });
            
            // Update notification count
            updateNotificationCount();
            
            // Hide "mark all as read" button
            const markAllBtn = document.querySelector('[onclick="markAllAsRead()"]');
            if (markAllBtn) markAllBtn.style.display = 'none';
        }
    })
    .catch(error => console.error('Error:', error));
}

function updateNotificationCount() {
    const unreadItems = document.querySelectorAll('.notification-item.bg-blue-50');
    const count = unreadItems.length;
    
    // Update bell icon badge
    const badge = document.querySelector('.fa-bell').parentElement.querySelector('.bg-red-500');
    if (badge) {
        if (count > 0) {
            badge.textContent = count;
        } else {
            badge.style.display = 'none';
        }
    }
    
    // Update dropdown header
    const newBadge = document.querySelector('.notification-dropdown .bg-blue-500');
    if (newBadge) {
        if (count > 0) {
            newBadge.textContent = `${count} new`;
        } else {
            newBadge.style.display = 'none';
        }
    }
}

// Close dropdown when clicking outside
document.addEventListener('click', function(event) {
    const dropdown = document.getElementById('notificationDropdown');
    const button = event.target.closest('[onclick="toggleNotifications()"]');
    
    if (!dropdown.contains(event.target) && !button) {
        dropdown.classList.add('hidden');
    }
});
</script>

<?php

namespace App\Modules\Dashboard\Controllers;

use App\Modules\Auth\Services\AuthService;
use App\Modules\Dashboard\Services\DashboardService;
use App\Modules\Dashboard\Services\WidgetService;
use App\Modules\Dashboard\Services\AnalyticsService;

/**
 * Dashboard Controller
 * Handles role-based dashboard requests
 */
class DashboardController
{
    private AuthService $authService;
    private DashboardService $dashboardService;
    private WidgetService $widgetService;
    private AnalyticsService $analyticsService;

    public function __construct()
    {
        $this->authService = new AuthService();
        $this->dashboardService = new DashboardService();
        $this->widgetService = new WidgetService();
        $this->analyticsService = new AnalyticsService();
    }

    /**
     * Show admin dashboard
     */
    public function admin()
    {
        // Check if user is logged in and has admin role
        if (!$this->authService->isLoggedIn() || $this->authService->getCurrentUserRole() !== 'admin') {
            return $this->redirect('/jobspace/login');
        }

        $user = $this->authService->getCurrentUser();
        $data = [
            'title' => 'Admin Dashboard - JobSpace',
            'user' => $user,
            'widgets' => $this->widgetService->getAdminWidgets(),
            'stats' => $this->analyticsService->getAdminStats(),
            'recent_activities' => $this->dashboardService->getRecentActivities('admin'),
            'system_health' => $this->dashboardService->getSystemHealth()
        ];

        return $this->renderView('admin/dashboard', $data);
    }

    /**
     * Show creator dashboard
     */
    public function creator()
    {
        // Check if user is logged in and has creator role
        if (!$this->authService->isLoggedIn() || $this->authService->getCurrentUserRole() !== 'creator') {
            return $this->redirect('/jobspace/login');
        }

        $user = $this->authService->getCurrentUser();
        $data = [
            'title' => 'Creator Dashboard - JobSpace',
            'user' => $user,
            'widgets' => $this->widgetService->getCreatorWidgets($user['id']),
            'stats' => $this->analyticsService->getCreatorStats($user['id']),
            'recent_activities' => $this->dashboardService->getRecentActivities('creator', $user['id']),
            'earnings' => $this->dashboardService->getEarnings($user['id'])
        ];

        return $this->renderView('creator/dashboard', $data);
    }

    /**
     * Show business dashboard
     */
    public function business()
    {
        // Check if user is logged in and has business role
        if (!$this->authService->isLoggedIn() || $this->authService->getCurrentUserRole() !== 'business') {
            return $this->redirect('/jobspace/login');
        }

        $user = $this->authService->getCurrentUser();
        $data = [
            'title' => 'Business Dashboard - JobSpace',
            'user' => $user,
            'widgets' => $this->widgetService->getBusinessWidgets($user['id']),
            'stats' => $this->analyticsService->getBusinessStats($user['id']),
            'recent_activities' => $this->dashboardService->getRecentActivities('business', $user['id']),
            'campaigns' => $this->dashboardService->getCampaigns($user['id'])
        ];

        return $this->renderView('business/dashboard', $data);
    }

    /**
     * Show user dashboard
     */
    public function user()
    {
        // Check if user is logged in and has user role
        if (!$this->authService->isLoggedIn() || $this->authService->getCurrentUserRole() !== 'user') {
            return $this->redirect('/jobspace/login');
        }

        $user = $this->authService->getCurrentUser();
        $data = [
            'title' => 'My Dashboard - JobSpace',
            'user' => $user,
            'widgets' => $this->widgetService->getUserWidgets($user['id']),
            'stats' => $this->analyticsService->getUserStats($user['id']),
            'recent_activities' => $this->dashboardService->getRecentActivities('user', $user['id']),
            'achievements' => $this->dashboardService->getAchievements($user['id']),
            'wallet' => $this->dashboardService->getWalletInfo($user['id'])
        ];

        return $this->renderView('user/dashboard', $data);
    }

    /**
     * Auto-redirect to appropriate dashboard based on role
     */
    public function index()
    {
        if (!$this->authService->isLoggedIn()) {
            return $this->redirect('/jobspace/login');
        }

        $role = $this->authService->getCurrentUserRole();
        
        switch ($role) {
            case 'admin':
                return $this->redirect('/jobspace/dashboard/admin');
            case 'creator':
                return $this->redirect('/jobspace/dashboard/creator');
            case 'business':
                return $this->redirect('/jobspace/dashboard/business');
            case 'user':
                return $this->redirect('/jobspace/dashboard/user');
            default:
                return $this->redirect('/jobspace/');
        }
    }

    /**
     * Get dashboard data via API
     */
    public function getDashboardData()
    {
        if (!$this->authService->isLoggedIn()) {
            return $this->jsonResponse(['error' => 'Unauthorized'], 401);
        }

        $role = $this->authService->getCurrentUserRole();
        $userId = $this->authService->getCurrentUserId();

        $data = [
            'widgets' => $this->widgetService->getWidgetsByRole($role, $userId),
            'stats' => $this->analyticsService->getStatsByRole($role, $userId),
            'activities' => $this->dashboardService->getRecentActivities($role, $userId)
        ];

        return $this->jsonResponse($data);
    }

    /**
     * Update widget settings
     */
    public function updateWidgetSettings()
    {
        if (!$this->authService->isLoggedIn()) {
            return $this->jsonResponse(['error' => 'Unauthorized'], 401);
        }

        $data = json_decode(file_get_contents('php://input'), true);
        $userId = $this->authService->getCurrentUserId();

        $result = $this->widgetService->updateSettings($userId, $data);

        return $this->jsonResponse($result);
    }

    /**
     * Render view
     */
    private function renderView(string $view, array $data = []): string
    {
        extract($data);
        ob_start();
        include BASE_PATH . "/app/modules/dashboard/views/{$view}.php";
        return ob_get_clean();
    }

    /**
     * JSON response
     */
    private function jsonResponse(array $data, int $status = 200): string
    {
        http_response_code($status);
        header('Content-Type: application/json');
        return json_encode($data);
    }

    /**
     * Redirect
     */
    private function redirect(string $url): void
    {
        header("Location: {$url}");
        exit;
    }
}

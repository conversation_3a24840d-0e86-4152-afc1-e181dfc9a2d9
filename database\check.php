<?php

require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../app/core/Database.php';

use App\Core\Database;

try {
    $db = Database::getInstance();
    
    echo "Database connection successful!\n\n";
    
    // Check users table
    $users = $db->select("SELECT * FROM users");
    
    echo "Users in database:\n";
    echo "==================\n";
    
    foreach ($users as $user) {
        echo "ID: " . $user['id'] . "\n";
        echo "Name: " . $user['name'] . "\n";
        echo "Email: " . $user['email'] . "\n";
        echo "Role: " . $user['role'] . "\n";
        echo "Status: " . $user['status'] . "\n";
        echo "Created: " . $user['created_at'] . "\n";
        echo "-------------------\n";
    }
    
    echo "\nTotal users: " . count($users) . "\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}

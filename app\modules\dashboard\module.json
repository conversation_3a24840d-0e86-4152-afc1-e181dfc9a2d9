{"name": "dashboard", "version": "1.0.0", "title": "Dashboard Module", "description": "Role-based dashboard system for Admin, Creator, Business, and User", "author": "JobSpace Team", "type": "core", "status": "active", "dependencies": ["auth"], "routes": {"web": "routes/web.php", "api": "routes/api.php"}, "config": {"main": "config/dashboard.php", "widgets": "config/widgets.php", "permissions": "config/permissions.php"}, "features": ["role-based-dashboards", "analytics-widgets", "real-time-stats", "activity-feeds", "quick-actions", "notifications", "profile-management", "settings-panel"], "roles": {"admin": {"dashboard": "admin", "features": ["*"], "widgets": ["all"]}, "creator": {"dashboard": "creator", "features": ["create_content", "analytics", "earnings"], "widgets": ["earnings", "content_stats", "performance"]}, "business": {"dashboard": "business", "features": ["create_ads", "analytics"], "widgets": ["ad_stats", "campaign_performance"]}, "user": {"dashboard": "user", "features": ["profile", "activities", "wallet"], "widgets": ["activities", "earnings", "achievements"]}}}
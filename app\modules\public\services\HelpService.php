<?php

namespace App\Modules\Public\Services;

/**
 * Help Service
 * Handles help and support functionality
 */
class HelpService
{
    /**
     * Get FAQ items
     */
    public function getFaqItems(): array
    {
        return [
            [
                'id' => 1,
                'question' => 'JobSpace কি?',
                'answer' => 'JobSpace হল একটি সম্পূর্ণ মডিউলার প্ল্যাটফর্ম যেখানে কুইজ, সোশ্যাল মিডিয়া, ই-কমার্স এবং ফ্রিল্যান্সিং সিস্টেম একসাথে রয়েছে।',
                'category' => 'general',
                'order' => 1
            ],
            [
                'id' => 2,
                'question' => 'কিভাবে রেজিস্টার করব?',
                'answer' => 'হোম পেজে "শুরু করুন" বাটনে ক্লিক করে আপনি সহজেই রেজিস্টার করতে পারবেন। রেজিস্ট্রেশন সম্পূর্ণ বিনামূল্যে।',
                'category' => 'account',
                'order' => 2
            ],
            [
                'id' => 3,
                'question' => 'কুইজ থেকে কিভাবে আয় করব?',
                'answer' => 'কুইজে সঠিক উত্তর দিয়ে আপনি পয়েন্ট এবং কয়েন অর্জন করতে পারবেন যা পরে টাকায় রূপান্তর করা যাবে।',
                'category' => 'quiz',
                'order' => 3
            ],
            [
                'id' => 4,
                'question' => 'পেমেন্ট কিভাবে পাব?',
                'answer' => 'আপনার উপার্জন সরাসরি আপনার ওয়ালেটে জমা হবে এবং সেখান থেকে মোবাইল ব্যাংকিং বা ব্যাংক অ্যাকাউন্টে উত্তোলন করতে পারবেন।',
                'category' => 'payment',
                'order' => 4
            ],
            [
                'id' => 5,
                'question' => 'কিভাবে প্রোফাইল আপডেট করব?',
                'answer' => 'লগইন করার পর ড্যাশবোর্ডে গিয়ে প্রোফাইল সেকশনে ক্লিক করে আপনার তথ্য আপডেট করতে পারবেন।',
                'category' => 'account',
                'order' => 5
            ]
        ];
    }

    /**
     * Get help categories
     */
    public function getHelpCategories(): array
    {
        return [
            [
                'id' => 1,
                'title' => 'শুরু করা',
                'icon' => 'fas fa-rocket',
                'color' => 'blue',
                'description' => 'প্ল্যাটফর্ম ব্যবহার শুরু করার গাইড',
                'topics' => [
                    'অ্যাকাউন্ট তৈরি করা',
                    'প্রোফাইল সেটআপ',
                    'প্রথম কুইজ খেলা',
                    'ড্যাশবোর্ড ব্যবহার'
                ]
            ],
            [
                'id' => 2,
                'title' => 'কুইজ সিস্টেম',
                'icon' => 'fas fa-brain',
                'color' => 'green',
                'description' => 'কুইজ খেলা এবং আয় করার নিয়ম',
                'topics' => [
                    'কুইজ খেলার নিয়ম',
                    'পয়েন্ট সিস্টেম',
                    'লিডারবোর্ড',
                    'কুইজ ক্যাটাগরি'
                ]
            ],
            [
                'id' => 3,
                'title' => 'পেমেন্ট ও ওয়ালেট',
                'icon' => 'fas fa-wallet',
                'color' => 'yellow',
                'description' => 'টাকা জমা ও উত্তোলনের নিয়ম',
                'topics' => [
                    'ওয়ালেট ব্যবহার',
                    'টাকা উত্তোলন',
                    'পেমেন্ট হিস্টরি',
                    'পেমেন্ট মেথড'
                ]
            ],
            [
                'id' => 4,
                'title' => 'সাপোর্ট',
                'icon' => 'fas fa-headset',
                'color' => 'purple',
                'description' => 'সাহায্য এবং সাপোর্ট পাওয়ার উপায়',
                'topics' => [
                    'যোগাযোগ করা',
                    'সমস্যা সমাধান',
                    'ফিডব্যাক দেওয়া',
                    'টিকেট সিস্টেম'
                ]
            ]
        ];
    }

    /**
     * Get support channels
     */
    public function getSupportChannels(): array
    {
        return [
            [
                'title' => 'ইমেইল সাপোর্ট',
                'description' => 'আমাদের সাপোর্ট টিমকে ইমেইল করুন',
                'icon' => 'fas fa-envelope',
                'color' => 'blue',
                'action_text' => 'যোগাযোগ করুন',
                'action_url' => '/jobspace/contact',
                'response_time' => '২৪ ঘন্টার মধ্যে'
            ],
            [
                'title' => 'লাইভ চ্যাট',
                'description' => 'তাৎক্ষণিক সাহায্যের জন্য চ্যাট করুন',
                'icon' => 'fas fa-comments',
                'color' => 'green',
                'action_text' => 'চ্যাট শুরু করুন',
                'action_url' => '#',
                'response_time' => 'তাৎক্ষণিক'
            ]
        ];
    }

    /**
     * Get video tutorials
     */
    public function getVideoTutorials(): array
    {
        return [
            [
                'title' => 'কিভাবে শুরু করবেন',
                'description' => 'JobSpace এ নতুন? এই ভিডিও দেখুন',
                'duration' => '৫ মিনিট',
                'thumbnail' => '/jobspace/public/assets/images/tutorials/getting-started.jpg',
                'video_url' => '#'
            ],
            [
                'title' => 'কুইজ খেলার নিয়ম',
                'description' => 'কুইজ খেলে কিভাবে আয় করবেন',
                'duration' => '৮ মিনিট',
                'thumbnail' => '/jobspace/public/assets/images/tutorials/quiz-guide.jpg',
                'video_url' => '#'
            ],
            [
                'title' => 'ওয়ালেট ব্যবহার',
                'description' => 'টাকা জমা ও উত্তোলনের নিয়ম',
                'duration' => '৬ মিনিট',
                'thumbnail' => '/jobspace/public/assets/images/tutorials/wallet-guide.jpg',
                'video_url' => '#'
            ],
            [
                'title' => 'সোশ্যাল ফিচার',
                'description' => 'বন্ধুদের সাথে যুক্ত হওয়ার উপায়',
                'duration' => '৭ মিনিট',
                'thumbnail' => '/jobspace/public/assets/images/tutorials/social-features.jpg',
                'video_url' => '#'
            ]
        ];
    }

    /**
     * Search FAQ by keyword
     */
    public function searchFaq(string $keyword): array
    {
        $allFaqs = $this->getFaqItems();
        $results = [];

        foreach ($allFaqs as $faq) {
            if (stripos($faq['question'], $keyword) !== false || 
                stripos($faq['answer'], $keyword) !== false) {
                $results[] = $faq;
            }
        }

        return $results;
    }

    /**
     * Get FAQ by category
     */
    public function getFaqByCategory(string $category): array
    {
        $allFaqs = $this->getFaqItems();
        return array_filter($allFaqs, function($faq) use ($category) {
            return $faq['category'] === $category;
        });
    }
}

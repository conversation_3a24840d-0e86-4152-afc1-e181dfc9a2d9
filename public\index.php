<?php

// JobSpace Entry Point with Modular Architecture Support
define('BASE_PATH', dirname(__DIR__));

// Load autoloader
require_once BASE_PATH . '/vendor/autoload.php';

// Load Module Manager
require_once BASE_PATH . '/app/core/ModuleManager.php';
$moduleManager = new \App\Core\ModuleManager();

// Routing using Module System
$uri = $_SERVER['REQUEST_URI'] ?? '/';
$method = $_SERVER['REQUEST_METHOD'] ?? 'GET';

// Remove base path for XAMPP
$uri = str_replace('/jobspace', '', $uri);
$uri = parse_url($uri, PHP_URL_PATH);

// Check if any modules are available
if (!$moduleManager->hasAnyModules()) {
    // Show landing page if no modules available
    include BASE_PATH . '/resources/views/landing.php';
    exit;
}

// Get primary module
$primaryModule = $moduleManager->getPrimaryModule();

// Load primary module dependencies
$moduleManager->loadModuleDependencies($primaryModule);

// Load module routes
$moduleRoutes = $moduleManager->loadModuleRoutes($primaryModule);

// Routes using Module System
if ($method === 'GET') {
    // Check if route exists in module
    if ($moduleRoutes && isset($moduleRoutes[$uri])) {
        $route = $moduleRoutes[$uri];
        if (is_array($route) && count($route) === 2) {
            [$controller, $method] = $route;
            echo $controller->$method();
        }
    } else {
        // Handle system routes (non-module routes)
        switch ($uri) {
            case '/test':
            header('Content-Type: application/json');
            echo json_encode([
                'status' => 'success',
                'message' => 'Framework is working perfectly!',
                'timestamp' => date('Y-m-d H:i:s'),
                'memory_usage' => memory_get_usage(true),
                'php_version' => PHP_VERSION,
                'framework' => 'JobSpace Modular Framework',
                'module' => $primaryModule
            ]);
            break;

        case '/api/status':
            header('Content-Type: application/json');
            echo json_encode([
                'status' => 'online',
                'version' => '1.0.0',
                'timestamp' => date('c'),
                'php_version' => PHP_VERSION,
                'framework' => 'JobSpace Modular Framework',
                'primary_module' => $primaryModule,
                'available_modules' => array_keys($moduleManager->getAvailableModules())
            ]);
            break;

        case '/api/health':
            header('Content-Type: application/json');
            echo json_encode([
                'status' => 'healthy',
                'timestamp' => date('c'),
                'memory_usage' => memory_get_usage(true),
                'modules_available' => $moduleManager->hasAnyModules(),
                'primary_module' => $primaryModule,
                'modules_count' => count($moduleManager->getAvailableModules())
            ]);
            break;

        case '/api/modules':
            header('Content-Type: application/json');
            echo json_encode([
                'modules' => $moduleManager->getAvailableModules(),
                'status' => $moduleManager->getModuleStatus()
            ]);
            break;

        default:
            http_response_code(404);
            echo '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>404 - Page Not Found</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100">
    <div class="min-h-screen flex items-center justify-center">
        <div class="bg-white p-8 rounded-lg shadow-md text-center">
            <h1 class="text-6xl font-bold text-red-500 mb-4">404</h1>
            <p class="text-gray-600 mb-4">Page not found</p>
            <a href="/jobspace/" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">Go Home</a>
        </div>
    </div>
</body>
</html>';
            break;
        }
    }
}

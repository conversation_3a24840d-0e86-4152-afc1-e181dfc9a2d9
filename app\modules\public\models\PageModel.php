<?php

namespace App\Modules\Public\Models;

class PageModel
{
    /**
     * Get page metadata
     */
    public static function getPageMeta(string $page): array
    {
        $metadata = [
            'home' => [
                'title' => 'JobSpace - Welcome',
                'description' => 'High-Performance Platform for Quiz, Social Media, E-commerce & Freelancing',
                'keywords' => 'jobspace, platform, quiz, social media, ecommerce, freelancing'
            ],
            'about' => [
                'title' => 'About - JobSpace',
                'description' => 'Learn more about JobSpace platform and its features',
                'keywords' => 'about jobspace, platform features, company info'
            ],
            'contact' => [
                'title' => 'Contact - JobSpace',
                'description' => 'Get in touch with JobSpace team',
                'keywords' => 'contact jobspace, support, help'
            ],
            'terms' => [
                'title' => 'Terms of Service - JobSpace',
                'description' => 'Terms and conditions for using JobSpace platform',
                'keywords' => 'terms of service, legal, conditions'
            ],
            'privacy' => [
                'title' => 'Privacy Policy - JobSpace',
                'description' => 'Privacy policy for JobSpace platform',
                'keywords' => 'privacy policy, data protection, privacy'
            ]
        ];

        return $metadata[$page] ?? [
            'title' => 'JobSpace',
            'description' => 'JobSpace Platform',
            'keywords' => 'jobspace'
        ];
    }

    /**
     * Get navigation menu
     */
    public static function getNavigation(): array
    {
        return [
            ['url' => '/jobspace/', 'title' => 'Home', 'active' => false],
            ['url' => '/jobspace/about', 'title' => 'About', 'active' => false],
            ['url' => '/jobspace/community', 'title' => 'Community', 'active' => false],
            ['url' => '/jobspace/help', 'title' => 'Help', 'active' => false],
            ['url' => '/jobspace/contact', 'title' => 'Contact', 'active' => false],
        ];
    }

    /**
     * Get footer links
     */
    public static function getFooterLinks(): array
    {
        return [
            'quick_links' => [
                ['url' => '/jobspace/', 'title' => 'Home'],
                ['url' => '/jobspace/about', 'title' => 'About'],
                ['url' => '/jobspace/contact', 'title' => 'Contact'],
                ['url' => '/jobspace/community', 'title' => 'Community'],
                ['url' => '/jobspace/help', 'title' => 'Help'],
            ],
            'legal' => [
                ['url' => '/jobspace/terms', 'title' => 'Terms of Service'],
                ['url' => '/jobspace/privacy', 'title' => 'Privacy Policy'],
            ]
        ];
    }

    /**
     * Get platform stats
     */
    public static function getPlatformStats(): array
    {
        return [
            ['value' => '50K+', 'label' => 'Concurrent Users', 'color' => 'blue'],
            ['value' => '<200ms', 'label' => 'Response Time', 'color' => 'green'],
            ['value' => '99.9%', 'label' => 'Uptime', 'color' => 'purple'],
            ['value' => '1M+', 'label' => 'Users Capacity', 'color' => 'yellow'],
        ];
    }

    /**
     * Get platform features
     */
    public static function getPlatformFeatures(): array
    {
        return [
            [
                'icon' => '🧠',
                'title' => 'Quiz System',
                'description' => '35 advanced features for creating and managing quizzes',
                'features' => ['Multiple question types', 'Real-time scoring', 'Analytics & reports']
            ],
            [
                'icon' => '📱',
                'title' => 'Social Media',
                'description' => '20 features for social networking platform',
                'features' => ['Posts & comments', 'Real-time feed', 'Follow system']
            ],
            [
                'icon' => '🛒',
                'title' => 'E-commerce',
                'description' => '15 features for online marketplace',
                'features' => ['Product catalog', 'Shopping cart', 'Payment gateway']
            ],
            [
                'icon' => '💼',
                'title' => 'Freelancing',
                'description' => '20 features for freelance marketplace',
                'features' => ['Job posting', 'Skill matching', 'Escrow system']
            ]
        ];
    }

    /**
     * Get community statistics
     */
    public static function getCommunityStats(): array
    {
        return [
            ['value' => '25,000+', 'label' => 'Active Members', 'icon' => 'fas fa-users', 'color' => 'blue'],
            ['value' => '150,000+', 'label' => 'Posts Created', 'icon' => 'fas fa-edit', 'color' => 'green'],
            ['value' => '500,000+', 'label' => 'Interactions', 'icon' => 'fas fa-heart', 'color' => 'red'],
            ['value' => '98%', 'label' => 'Satisfaction Rate', 'icon' => 'fas fa-star', 'color' => 'yellow'],
        ];
    }

    /**
     * Get recent community activities
     */
    public static function getRecentActivities(): array
    {
        return [
            [
                'user' => 'রহিম আহমেদ',
                'action' => 'একটি নতুন কুইজ তৈরি করেছেন',
                'time' => '২ ঘন্টা আগে',
                'type' => 'quiz'
            ],
            [
                'user' => 'ফাতেমা বেগম',
                'action' => 'একটি পোস্ট শেয়ার করেছেন',
                'time' => '৪ ঘন্টা আগে',
                'type' => 'social'
            ],
            [
                'user' => 'করিম উদ্দিন',
                'action' => 'একটি প্রোডাক্ট লিস্ট করেছেন',
                'time' => '৬ ঘন্টা আগে',
                'type' => 'ecommerce'
            ],
            [
                'user' => 'সাজিদ হাসান',
                'action' => 'একটি জব পোস্ট করেছেন',
                'time' => '৮ ঘন্টা আগে',
                'type' => 'freelance'
            ]
        ];
    }

    /**
     * Get FAQ items
     */
    public static function getFaqItems(): array
    {
        return [
            [
                'question' => 'JobSpace কি?',
                'answer' => 'JobSpace হল একটি সম্পূর্ণ মডিউলার প্ল্যাটফর্ম যেখানে কুইজ, সোশ্যাল মিডিয়া, ই-কমার্স এবং ফ্রিল্যান্সিং সিস্টেম একসাথে রয়েছে।'
            ],
            [
                'question' => 'কিভাবে রেজিস্টার করব?',
                'answer' => 'হোম পেজে "শুরু করুন" বাটনে ক্লিক করে আপনি সহজেই রেজিস্টার করতে পারবেন। রেজিস্ট্রেশন সম্পূর্ণ বিনামূল্যে।'
            ],
            [
                'question' => 'কুইজ থেকে কিভাবে আয় করব?',
                'answer' => 'কুইজে সঠিক উত্তর দিয়ে আপনি পয়েন্ট এবং কয়েন অর্জন করতে পারবেন যা পরে টাকায় রূপান্তর করা যাবে।'
            ],
            [
                'question' => 'পেমেন্ট কিভাবে পাব?',
                'answer' => 'আপনার উপার্জন সরাসরি আপনার ওয়ালেটে জমা হবে এবং সেখান থেকে মোবাইল ব্যাংকিং বা ব্যাংক অ্যাকাউন্টে উত্তোলন করতে পারবেন।'
            ]
        ];
    }

    /**
     * Get help categories
     */
    public static function getHelpCategories(): array
    {
        return [
            [
                'title' => 'শুরু করা',
                'icon' => 'fas fa-rocket',
                'color' => 'blue',
                'topics' => [
                    'অ্যাকাউন্ট তৈরি করা',
                    'প্রোফাইল সেটআপ',
                    'প্রথম কুইজ খেলা'
                ]
            ],
            [
                'title' => 'কুইজ সিস্টেম',
                'icon' => 'fas fa-brain',
                'color' => 'green',
                'topics' => [
                    'কুইজ খেলার নিয়ম',
                    'পয়েন্ট সিস্টেম',
                    'লিডারবোর্ড'
                ]
            ],
            [
                'title' => 'পেমেন্ট ও ওয়ালেট',
                'icon' => 'fas fa-wallet',
                'color' => 'yellow',
                'topics' => [
                    'ওয়ালেট ব্যবহার',
                    'টাকা উত্তোলন',
                    'পেমেন্ট হিস্টরি'
                ]
            ],
            [
                'title' => 'সাপোর্ট',
                'icon' => 'fas fa-headset',
                'color' => 'purple',
                'topics' => [
                    'যোগাযোগ করা',
                    'সমস্যা সমাধান',
                    'ফিডব্যাক দেওয়া'
                ]
            ]
        ];
    }
}

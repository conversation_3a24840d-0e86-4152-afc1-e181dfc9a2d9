<?php

namespace App\Modules\Auth\Services;

use App\Modules\Auth\Models\UserModel;

/**
 * Validation Service
 * Handles form validation for authentication
 */
class ValidationService
{
    private array $validationRules;

    public function __construct()
    {
        $this->validationRules = include BASE_PATH . '/app/modules/auth/config/validation.php';
    }

    /**
     * Validate registration step 1
     */
    public function validateRegistrationStep1(array $data): array
    {
        $rules = $this->validationRules['registration_step1'];
        $errors = [];
        $validatedData = [];

        foreach ($rules as $field => $rule) {
            $value = $data[$field] ?? '';
            $fieldErrors = $this->validateField($field, $value, $rule);
            
            if (!empty($fieldErrors)) {
                $errors[$field] = $fieldErrors[0]; // Take first error
            } else {
                $validatedData[$field] = $this->sanitizeValue($value, $rule);
            }
        }

        return [
            'success' => empty($errors),
            'errors' => $errors,
            'data' => $validatedData
        ];
    }

    /**
     * Validate registration step 2
     */
    public function validateRegistrationStep2(array $data): array
    {
        $rules = $this->validationRules['registration_step2'];
        $errors = [];
        $validatedData = [];

        foreach ($rules as $field => $rule) {
            $value = $data[$field] ?? '';
            $fieldErrors = $this->validateField($field, $value, $rule);
            
            if (!empty($fieldErrors)) {
                $errors[$field] = $fieldErrors[0]; // Take first error
            } else {
                $validatedData[$field] = $this->sanitizeValue($value, $rule);
            }
        }

        return [
            'success' => empty($errors),
            'errors' => $errors,
            'data' => $validatedData
        ];
    }

    /**
     * Validate login
     */
    public function validateLogin(array $data): array
    {
        $rules = $this->validationRules['login'];
        $errors = [];
        $validatedData = [];

        foreach ($rules as $field => $rule) {
            $value = $data[$field] ?? '';
            $fieldErrors = $this->validateField($field, $value, $rule);
            
            if (!empty($fieldErrors)) {
                $errors[$field] = $fieldErrors[0]; // Take first error
            } else {
                $validatedData[$field] = $this->sanitizeValue($value, $rule);
            }
        }

        return [
            'success' => empty($errors),
            'errors' => $errors,
            'data' => $validatedData
        ];
    }

    /**
     * Validate individual field
     */
    private function validateField(string $field, $value, array $rule): array
    {
        $errors = [];

        // Required validation
        if (isset($rule['required']) && $rule['required'] && empty($value)) {
            $errors[] = $rule['message'] ?? "{$field} is required";
            return $errors; // Return early if required field is empty
        }

        // Skip other validations if field is empty and not required
        if (empty($value) && (!isset($rule['required']) || !$rule['required'])) {
            return $errors;
        }

        // Type validation
        if (isset($rule['type'])) {
            if (!$this->validateType($value, $rule['type'])) {
                $errors[] = $rule['message'] ?? "Invalid {$rule['type']} format";
            }
        }

        // Length validations
        if (isset($rule['min_length']) && strlen($value) < $rule['min_length']) {
            $errors[] = $rule['message'] ?? "Minimum length is {$rule['min_length']}";
        }

        if (isset($rule['max_length']) && strlen($value) > $rule['max_length']) {
            $errors[] = $rule['message'] ?? "Maximum length is {$rule['max_length']}";
        }

        // Pattern validation
        if (isset($rule['pattern']) && !preg_match($rule['pattern'], $value)) {
            $errors[] = $rule['message'] ?? "Invalid format";
        }

        // Options validation
        if (isset($rule['options']) && !in_array($value, $rule['options'])) {
            $errors[] = $rule['message'] ?? "Invalid option selected";
        }

        // Unique validation
        if (isset($rule['unique'])) {
            if (!$this->validateUnique($value, $rule['unique'])) {
                $errors[] = $rule['message'] ?? "This value already exists";
            }
        }

        // Exists validation
        if (isset($rule['exists'])) {
            if (!$this->validateExists($value, $rule['exists'])) {
                $errors[] = $rule['message'] ?? "This value does not exist";
            }
        }

        // Match validation (for password confirmation)
        if (isset($rule['match'])) {
            $matchField = $rule['match'];
            $matchValue = $_POST[$matchField] ?? '';
            if ($value !== $matchValue) {
                $errors[] = $rule['message'] ?? "Values do not match";
            }
        }

        // Value validation (for checkboxes)
        if (isset($rule['value']) && $value != $rule['value']) {
            $errors[] = $rule['message'] ?? "Invalid value";
        }

        // Age validation
        if (isset($rule['min_age'])) {
            if (!$this->validateMinAge($value, $rule['min_age'])) {
                $errors[] = $rule['message'] ?? "Minimum age is {$rule['min_age']}";
            }
        }

        return $errors;
    }

    /**
     * Validate data type
     */
    private function validateType($value, string $type): bool
    {
        switch ($type) {
            case 'email':
                return filter_var($value, FILTER_VALIDATE_EMAIL) !== false;
            case 'date':
                return strtotime($value) !== false;
            case 'boolean':
                return is_bool($value) || in_array($value, ['true', 'false', '1', '0', 1, 0]);
            case 'file':
                return isset($_FILES[$value]) && $_FILES[$value]['error'] === UPLOAD_ERR_OK;
            default:
                return true;
        }
    }

    /**
     * Validate unique constraint
     */
    private function validateUnique($value, string $constraint): bool
    {
        [$table, $column] = explode('.', $constraint);
        
        switch ($table) {
            case 'users':
                switch ($column) {
                    case 'email':
                        return !UserModel::emailExists($value);
                    case 'username':
                        return !UserModel::usernameExists($value);
                    case 'phone':
                        return !UserModel::phoneExists($value);
                }
                break;
        }
        
        return true;
    }

    /**
     * Validate exists constraint
     */
    private function validateExists($value, string $constraint): bool
    {
        [$table, $column] = explode('.', $constraint);
        
        switch ($table) {
            case 'users':
                switch ($column) {
                    case 'email':
                        return UserModel::emailExists($value);
                    case 'referral_code':
                        return UserModel::findByReferralCode($value) !== null;
                }
                break;
        }
        
        return false;
    }

    /**
     * Validate minimum age
     */
    private function validateMinAge($dateOfBirth, int $minAge): bool
    {
        $birthDate = new DateTime($dateOfBirth);
        $today = new DateTime();
        $age = $today->diff($birthDate)->y;
        
        return $age >= $minAge;
    }

    /**
     * Sanitize value
     */
    private function sanitizeValue($value, array $rule)
    {
        // Basic sanitization
        if (is_string($value)) {
            $value = trim($value);
            $value = htmlspecialchars($value, ENT_QUOTES, 'UTF-8');
        }

        // Type-specific sanitization
        if (isset($rule['type'])) {
            switch ($rule['type']) {
                case 'email':
                    $value = filter_var($value, FILTER_SANITIZE_EMAIL);
                    break;
                case 'boolean':
                    $value = in_array($value, ['true', '1', 1, true], true);
                    break;
            }
        }

        return $value;
    }

    /**
     * Get validation rules for a specific form
     */
    public function getRules(string $form): array
    {
        return $this->validationRules[$form] ?? [];
    }
}

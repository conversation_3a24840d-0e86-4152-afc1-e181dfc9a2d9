<?php

namespace App\Modules\Public\Services;

/**
 * Navigation Service
 * Handles all navigation-related functionality
 */
class NavigationService
{
    private array $navigationConfig;

    public function __construct()
    {
        $this->navigationConfig = include BASE_PATH . '/app/modules/public/config/navigation.php';
    }

    /**
     * Get main navigation menu
     */
    public function getMainMenu(): array
    {
        $menu = $this->navigationConfig['main_menu'];
        
        // Set active state based on current URL
        $currentUri = $_SERVER['REQUEST_URI'] ?? '/';
        $currentUri = parse_url($currentUri, PHP_URL_PATH);
        
        foreach ($menu as &$item) {
            $item['active'] = $this->isCurrentPage($item['url'], $currentUri);
        }
        
        // Sort by order
        usort($menu, function($a, $b) {
            return $a['order'] <=> $b['order'];
        });
        
        return $menu;
    }

    /**
     * Get footer links
     */
    public function getFooterLinks(): array
    {
        $footerLinks = $this->navigationConfig['footer_links'];
        
        // Sort each section by order
        foreach ($footerLinks as &$section) {
            if (is_array($section)) {
                usort($section, function($a, $b) {
                    return ($a['order'] ?? 0) <=> ($b['order'] ?? 0);
                });
            }
        }
        
        return $footerLinks;
    }

    /**
     * Generate breadcrumb for current page
     */
    public function getBreadcrumb(string $currentPage): array
    {
        $settings = $this->navigationConfig['breadcrumb_settings'];
        
        if (!$settings['enabled']) {
            return [];
        }
        
        $breadcrumb = [
            [
                'title' => $settings['home_text'],
                'url' => '/jobspace/',
                'active' => false
            ]
        ];
        
        // Add current page if not home
        if ($currentPage !== 'home') {
            $pageTitle = $this->getPageTitle($currentPage);
            $breadcrumb[] = [
                'title' => $pageTitle,
                'url' => '',
                'active' => true
            ];
        } else {
            $breadcrumb[0]['active'] = true;
        }
        
        return $breadcrumb;
    }

    /**
     * Check if current page matches URL
     */
    private function isCurrentPage(string $url, string $currentUri): bool
    {
        $urlPath = parse_url($url, PHP_URL_PATH);
        return $currentUri === $urlPath;
    }

    /**
     * Get page title by page name
     */
    private function getPageTitle(string $pageName): string
    {
        $titles = [
            'about' => 'About',
            'community' => 'Community',
            'help' => 'Help',
            'contact' => 'Contact',
            'terms' => 'Terms',
            'privacy' => 'Privacy'
        ];
        
        return $titles[$pageName] ?? ucfirst($pageName);
    }

    /**
     * Get navigation item by URL
     */
    public function getNavigationItem(string $url): ?array
    {
        foreach ($this->navigationConfig['main_menu'] as $item) {
            if ($item['url'] === $url) {
                return $item;
            }
        }
        
        return null;
    }

    /**
     * Check if navigation item exists
     */
    public function hasNavigationItem(string $url): bool
    {
        return $this->getNavigationItem($url) !== null;
    }
}

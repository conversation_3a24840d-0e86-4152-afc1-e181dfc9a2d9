<?php

/**
 * Web Routes
 * Define your web application routes here
 */

// Get router instance directly
$router = \App\Core\Application::getInstance()->router();



// Home route (root)
$router->get('/', function() {
    return response('
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JobSpace - Welcome</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100">
    <div class="min-h-screen flex items-center justify-center">
        <div class="bg-white p-8 rounded-lg shadow-md text-center max-w-md">
            <h1 class="text-3xl font-bold text-gray-800 mb-4">🚀 JobSpace</h1>
            <p class="text-green-600 mb-4">✅ System Successfully Launched!</p>
            <p class="text-sm text-gray-500 mb-4">High-Performance Framework Ready</p>
            <div class="text-xs text-gray-400 space-y-1">
                <p>📊 Supports 50K+ Concurrent Users</p>
                <p>🧩 Modular Architecture Active</p>
                <p>⚡ Optimized for Performance</p>
                <p>🔒 Security Features Enabled</p>
            </div>
            <div class="mt-6 space-y-2">
                <a href="/test" class="block bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 transition">Test Route</a>
                <a href="/api/status" class="block bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 transition">API Status</a>
            </div>
        </div>
    </div>
</body>
</html>
    ');
})->name('home');



// Test route
$router->get('/test', function() {
    return json([
        'status' => 'success',
        'message' => 'Framework is working perfectly!',
        'timestamp' => date('Y-m-d H:i:s'),
        'memory_usage' => memory_get_usage(true),
        'peak_memory' => memory_get_peak_usage(true),
        'execution_time' => microtime(true) - $_SERVER['REQUEST_TIME_FLOAT'],
        'php_version' => PHP_VERSION,
        'framework' => 'JobSpace Custom Framework',
        'features' => [
            'High Performance',
            'Modular Architecture',
            'Security Features',
            'Caching System',
            'Database Optimization',
            'Session Management'
        ]
    ]);
})->name('test');

// Health check route
$router->get('/health', function() {
    $health = [
        'status' => 'healthy',
        'timestamp' => date('c'),
        'uptime' => time() - $_SERVER['REQUEST_TIME'],
        'memory' => [
            'current' => memory_get_usage(true),
            'peak' => memory_get_peak_usage(true),
            'limit' => ini_get('memory_limit')
        ],
        'system' => [
            'php_version' => PHP_VERSION,
            'os' => PHP_OS,
            'server' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'
        ]
    ];

    return json($health);
})->name('health');

// About route
$router->get('/about', function() {
    return response('
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>About - JobSpace</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100">
    <div class="min-h-screen py-12">
        <div class="max-w-4xl mx-auto px-4">
            <div class="bg-white rounded-lg shadow-lg p-8">
                <h1 class="text-4xl font-bold text-gray-800 mb-6">About JobSpace</h1>

                <div class="grid md:grid-cols-2 gap-8">
                    <div>
                        <h2 class="text-2xl font-semibold text-gray-700 mb-4">🎯 Platform Features</h2>
                        <ul class="space-y-2 text-gray-600">
                            <li>• Quiz System (35 features)</li>
                            <li>• Social Media Platform (20 features)</li>
                            <li>• E-commerce System (15 features)</li>
                            <li>• Freelancing Marketplace (20 features)</li>
                            <li>• Wallet & Payment System</li>
                            <li>• Unified Feed System</li>
                            <li>• Real-time Notifications</li>
                        </ul>
                    </div>

                    <div>
                        <h2 class="text-2xl font-semibold text-gray-700 mb-4">⚡ Performance</h2>
                        <ul class="space-y-2 text-gray-600">
                            <li>• Supports 50K+ concurrent users</li>
                            <li>• <200ms response time</li>
                            <li>• 99.9% uptime target</li>
                            <li>• Advanced caching system</li>
                            <li>• Database optimization</li>
                            <li>• Horizontal scaling ready</li>
                        </ul>
                    </div>
                </div>

                <div class="mt-8">
                    <h2 class="text-2xl font-semibent text-gray-700 mb-4">🏗️ Architecture</h2>
                    <p class="text-gray-600 mb-4">
                        JobSpace is built with a modular architecture that allows each component to work independently
                        while seamlessly integrating with other modules. The platform uses microservices-based design
                        patterns for maximum scalability and maintainability.
                    </p>
                    <div class="bg-gray-50 p-4 rounded">
                        <h3 class="font-semibold text-gray-700 mb-2">Core Technologies:</h3>
                        <p class="text-sm text-gray-600">
                            Pure PHP 8+, MariaDB with InnoDB, Custom MVC Framework,
                            File-based Smart Caching, Tailwind CSS, Apache/Nginx
                        </p>
                    </div>
                </div>

                <div class="mt-8 text-center">
                    <a href="/" class="bg-blue-500 text-white px-6 py-3 rounded-lg hover:bg-blue-600 transition">
                        Back to Home
                    </a>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
    ');
})->name('about');

// Auto-discover and load module routes
$modulesPath = BASE_PATH . '/app/modules';

if (is_dir($modulesPath)) {
    $modules = scandir($modulesPath);

    foreach ($modules as $module) {
        if ($module === '.' || $module === '..') {
            continue;
        }

        $moduleRoutesFile = $modulesPath . '/' . $module . '/routes/web.php';

        if (file_exists($moduleRoutesFile)) {
            require_once $moduleRoutesFile;
        }
    }
}
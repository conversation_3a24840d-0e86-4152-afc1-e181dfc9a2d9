# JobSpace Modular PHP Platform

A highly scalable, secure, SEO-friendly, and modular multi-role web platform built with PHP, MariaDB, Bootstrap 5, and PHPMailer.

## Features
- Multi-role authentication (admin, creator, user, guest)
- Modular MVC structure for pluggable features
- Dynamic layouts and reusable components
- Quiz, social, and ad systems (starter modules)
- PHPMailer integration
- SEO optimized, responsive, and accessible
- Caching, error handling, and best practices

## Getting Started
1. Copy `.env.example` to `.env` and set your environment variables.
2. Configure your web server to use `public/` as the document root.
3. Run migrations and seeders in `database/` as needed.
4. Start building your features in `app/features/` and `resources/views/features/`.

## Folder Structure
See the project root for a detailed breakdown.

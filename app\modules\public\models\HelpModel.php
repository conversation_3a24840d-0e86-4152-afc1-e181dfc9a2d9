<?php

namespace App\Modules\Public\Models;

/**
 * Help Model
 * Handles help and support data operations
 */
class HelpModel
{
    /**
     * Get all FAQ items
     */
    public static function getFaqItems(): array
    {
        // In real application, this would fetch from database
        return [
            [
                'id' => 1,
                'question' => 'JobSpace কি?',
                'answer' => 'JobSpace হল একটি সম্পূর্ণ মডিউলার প্ল্যাটফর্ম যেখানে কুইজ, সোশ্যাল মিডিয়া, ই-কমার্স এবং ফ্রিল্যান্সিং সিস্টেম একসাথে রয়েছে।',
                'category' => 'general',
                'order' => 1,
                'is_active' => true,
                'created_at' => '2024-01-01 00:00:00'
            ],
            [
                'id' => 2,
                'question' => 'কিভাবে রেজিস্টার করব?',
                'answer' => 'হোম পেজে "শুরু করুন" বাটনে ক্লিক করে আপনি সহজেই রেজিস্টার করতে পারবেন। রেজিস্ট্রেশন সম্পূর্ণ বিনামূল্যে।',
                'category' => 'account',
                'order' => 2,
                'is_active' => true,
                'created_at' => '2024-01-01 00:00:00'
            ],
            [
                'id' => 3,
                'question' => 'কুইজ থেকে কিভাবে আয় করব?',
                'answer' => 'কুইজে সঠিক উত্তর দিয়ে আপনি পয়েন্ট এবং কয়েন অর্জন করতে পারবেন যা পরে টাকায় রূপান্তর করা যাবে।',
                'category' => 'quiz',
                'order' => 3,
                'is_active' => true,
                'created_at' => '2024-01-01 00:00:00'
            ],
            [
                'id' => 4,
                'question' => 'পেমেন্ট কিভাবে পাব?',
                'answer' => 'আপনার উপার্জন সরাসরি আপনার ওয়ালেটে জমা হবে এবং সেখান থেকে মোবাইল ব্যাংকিং বা ব্যাংক অ্যাকাউন্টে উত্তোলন করতে পারবেন।',
                'category' => 'payment',
                'order' => 4,
                'is_active' => true,
                'created_at' => '2024-01-01 00:00:00'
            ]
        ];
    }

    /**
     * Get help categories
     */
    public static function getHelpCategories(): array
    {
        return [
            [
                'id' => 1,
                'title' => 'শুরু করা',
                'icon' => 'fas fa-rocket',
                'color' => 'blue',
                'description' => 'প্ল্যাটফর্ম ব্যবহার শুরু করার গাইড',
                'order' => 1,
                'topics' => [
                    'অ্যাকাউন্ট তৈরি করা',
                    'প্রোফাইল সেটআপ',
                    'প্রথম কুইজ খেলা',
                    'ড্যাশবোর্ড ব্যবহার'
                ]
            ],
            [
                'id' => 2,
                'title' => 'কুইজ সিস্টেম',
                'icon' => 'fas fa-brain',
                'color' => 'green',
                'description' => 'কুইজ খেলা এবং আয় করার নিয়ম',
                'order' => 2,
                'topics' => [
                    'কুইজ খেলার নিয়ম',
                    'পয়েন্ট সিস্টেম',
                    'লিডারবোর্ড',
                    'কুইজ ক্যাটাগরি'
                ]
            ],
            [
                'id' => 3,
                'title' => 'পেমেন্ট ও ওয়ালেট',
                'icon' => 'fas fa-wallet',
                'color' => 'yellow',
                'description' => 'টাকা জমা ও উত্তোলনের নিয়ম',
                'order' => 3,
                'topics' => [
                    'ওয়ালেট ব্যবহার',
                    'টাকা উত্তোলন',
                    'পেমেন্ট হিস্টরি',
                    'পেমেন্ট মেথড'
                ]
            ],
            [
                'id' => 4,
                'title' => 'সাপোর্ট',
                'icon' => 'fas fa-headset',
                'color' => 'purple',
                'description' => 'সাহায্য এবং সাপোর্ট পাওয়ার উপায়',
                'order' => 4,
                'topics' => [
                    'যোগাযোগ করা',
                    'সমস্যা সমাধান',
                    'ফিডব্যাক দেওয়া',
                    'টিকেট সিস্টেম'
                ]
            ]
        ];
    }

    /**
     * Get FAQ by ID
     */
    public static function getFaqById(int $id): ?array
    {
        $faqs = self::getFaqItems();
        
        foreach ($faqs as $faq) {
            if ($faq['id'] === $id) {
                return $faq;
            }
        }
        
        return null;
    }

    /**
     * Get FAQ by category
     */
    public static function getFaqByCategory(string $category): array
    {
        $faqs = self::getFaqItems();
        
        return array_filter($faqs, function($faq) use ($category) {
            return $faq['category'] === $category && $faq['is_active'];
        });
    }

    /**
     * Search FAQ
     */
    public static function searchFaq(string $keyword): array
    {
        $faqs = self::getFaqItems();
        $results = [];

        foreach ($faqs as $faq) {
            if (!$faq['is_active']) continue;
            
            if (stripos($faq['question'], $keyword) !== false || 
                stripos($faq['answer'], $keyword) !== false) {
                $results[] = $faq;
            }
        }

        return $results;
    }

    /**
     * Get help category by ID
     */
    public static function getHelpCategoryById(int $id): ?array
    {
        $categories = self::getHelpCategories();
        
        foreach ($categories as $category) {
            if ($category['id'] === $id) {
                return $category;
            }
        }
        
        return null;
    }

    /**
     * Get ordered help categories
     */
    public static function getOrderedHelpCategories(): array
    {
        $categories = self::getHelpCategories();
        
        usort($categories, function($a, $b) {
            return ($a['order'] ?? 0) <=> ($b['order'] ?? 0);
        });
        
        return $categories;
    }

    /**
     * Get FAQ categories list
     */
    public static function getFaqCategories(): array
    {
        return [
            'general' => 'সাধারণ',
            'account' => 'অ্যাকাউন্ট',
            'quiz' => 'কুইজ',
            'payment' => 'পেমেন্ট',
            'social' => 'সোশ্যাল',
            'ecommerce' => 'ই-কমার্স',
            'freelance' => 'ফ্রিল্যান্স'
        ];
    }
}

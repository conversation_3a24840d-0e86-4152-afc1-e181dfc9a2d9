<?php

namespace App\Modules\Public\Models;

/**
 * Community Model
 * Handles community data operations
 */
class CommunityModel
{
    /**
     * Get community statistics from database or cache
     */
    public static function getCommunityStats(): array
    {
        // In real application, this would fetch from database
        // For now, returning static data
        return [
            [
                'value' => '25,000+',
                'label' => 'Active Members',
                'icon' => 'fas fa-users',
                'color' => 'blue',
                'count' => 25000
            ],
            [
                'value' => '150,000+',
                'label' => 'Posts Created',
                'icon' => 'fas fa-edit',
                'color' => 'green',
                'count' => 150000
            ],
            [
                'value' => '500,000+',
                'label' => 'Interactions',
                'icon' => 'fas fa-heart',
                'color' => 'red',
                'count' => 500000
            ],
            [
                'value' => '98%',
                'label' => 'Satisfaction Rate',
                'icon' => 'fas fa-star',
                'color' => 'yellow',
                'count' => 98
            ]
        ];
    }

    /**
     * Get recent community activities
     */
    public static function getRecentActivities(int $limit = 10): array
    {
        // In real application, this would fetch from database
        // For now, returning static data
        $activities = [
            [
                'id' => 1,
                'user_id' => 1,
                'user' => 'রহিম আহমেদ',
                'action' => 'একটি নতুন কুইজ তৈরি করেছেন',
                'time' => '২ ঘন্টা আগে',
                'type' => 'quiz',
                'created_at' => date('Y-m-d H:i:s', strtotime('-2 hours'))
            ],
            [
                'id' => 2,
                'user_id' => 2,
                'user' => 'ফাতেমা বেগম',
                'action' => 'একটি পোস্ট শেয়ার করেছেন',
                'time' => '৪ ঘন্টা আগে',
                'type' => 'social',
                'created_at' => date('Y-m-d H:i:s', strtotime('-4 hours'))
            ],
            [
                'id' => 3,
                'user_id' => 3,
                'user' => 'করিম উদ্দিন',
                'action' => 'একটি প্রোডাক্ট লিস্ট করেছেন',
                'time' => '৬ ঘন্টা আগে',
                'type' => 'ecommerce',
                'created_at' => date('Y-m-d H:i:s', strtotime('-6 hours'))
            ],
            [
                'id' => 4,
                'user_id' => 4,
                'user' => 'সাজিদ হাসান',
                'action' => 'একটি জব পোস্ট করেছেন',
                'time' => '৮ ঘন্টা আগে',
                'type' => 'freelance',
                'created_at' => date('Y-m-d H:i:s', strtotime('-8 hours'))
            ],
            [
                'id' => 5,
                'user_id' => 5,
                'user' => 'নাদিয়া খান',
                'action' => 'একটি কুইজে অংশগ্রহণ করেছেন',
                'time' => '১০ ঘন্টা আগে',
                'type' => 'quiz',
                'created_at' => date('Y-m-d H:i:s', strtotime('-10 hours'))
            ]
        ];

        return array_slice($activities, 0, $limit);
    }

    /**
     * Get community member count
     */
    public static function getMemberCount(): int
    {
        // In real application, this would count from users table
        return 25000;
    }

    /**
     * Get total posts count
     */
    public static function getPostsCount(): int
    {
        // In real application, this would count from posts table
        return 150000;
    }

    /**
     * Get total interactions count
     */
    public static function getInteractionsCount(): int
    {
        // In real application, this would count from interactions table
        return 500000;
    }

    /**
     * Get satisfaction rate
     */
    public static function getSatisfactionRate(): float
    {
        // In real application, this would calculate from feedback/ratings
        return 98.0;
    }

    /**
     * Get activities by type
     */
    public static function getActivitiesByType(string $type, int $limit = 10): array
    {
        $allActivities = self::getRecentActivities(100);
        
        $filtered = array_filter($allActivities, function($activity) use ($type) {
            return $activity['type'] === $type;
        });

        return array_slice($filtered, 0, $limit);
    }

    /**
     * Get activities by user
     */
    public static function getActivitiesByUser(int $userId, int $limit = 10): array
    {
        $allActivities = self::getRecentActivities(100);
        
        $filtered = array_filter($allActivities, function($activity) use ($userId) {
            return $activity['user_id'] === $userId;
        });

        return array_slice($filtered, 0, $limit);
    }
}

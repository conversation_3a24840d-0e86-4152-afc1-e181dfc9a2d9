{"name": "auth", "version": "1.0.0", "title": "Authentication Module", "description": "Handles user authentication, registration, and session management", "author": "JobSpace Team", "type": "core", "status": "active", "dependencies": [], "routes": {"web": "routes/web.php", "api": "routes/api.php"}, "config": {"main": "config/auth.php", "session": "config/session.php", "validation": "config/validation.php"}, "features": ["multi-step-registration", "role-based-login", "session-management", "password-reset", "email-verification", "social-login", "two-factor-auth", "remember-me", "account-lockout", "login-history"], "permissions": ["auth.login", "auth.register", "auth.logout", "auth.reset-password", "auth.verify-email"], "database": {"tables": ["users", "user_sessions", "password_resets", "email_verifications", "login_attempts"]}, "cache": {"enabled": true, "duration": 3600, "keys": ["user_session", "login_attempts", "user_permissions"]}}
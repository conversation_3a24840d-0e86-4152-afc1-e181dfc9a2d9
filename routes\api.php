<?php

/**
 * API Application Routes
 * Define your API application routes here
 */

// Get router instance directly
$router = \App\Core\Application::getInstance()->router();

// API route group with prefix
$router->group(['prefix' => 'api'], function($router) {

    // API Status
    $router->get('/status', function() {
        return json([
            'status' => 'online',
            'version' => '1.0.0',
            'timestamp' => date('c'),
            'server_time' => time(),
            'timezone' => date_default_timezone_get(),
            'environment' => config('app.env', 'production'),
            'debug_mode' => config('app.debug', false),
            'php_version' => PHP_VERSION,
            'memory_usage' => [
                'current' => memory_get_usage(true),
                'peak' => memory_get_peak_usage(true),
                'limit' => ini_get('memory_limit')
            ],
            'performance' => [
                'execution_time' => microtime(true) - $_SERVER['REQUEST_TIME_FLOAT'],
                'opcache_enabled' => function_exists('opcache_get_status') && opcache_get_status(),
                'gzip_enabled' => extension_loaded('zlib')
            ],
            'database' => [
                'status' => 'connected', // This would be checked in real implementation
                'driver' => config('database.default', 'mysql')
            ],
            'cache' => [
                'status' => 'active',
                'driver' => config('cache.default', 'file')
            ],
            'features' => [
                'quiz_system' => true,
                'social_media' => true,
                'ecommerce' => true,
                'freelancing' => true,
                'wallet_system' => true,
                'notifications' => true,
                'feed_system' => true
            ]
        ]);
    })->name('api.status');

    // API Health Check
    $router->get('/health', function() {
        $health = [
            'status' => 'healthy',
            'checks' => [
                'database' => 'ok',
                'cache' => 'ok',
                'session' => 'ok',
                'filesystem' => 'ok'
            ],
            'metrics' => [
                'response_time' => microtime(true) - $_SERVER['REQUEST_TIME_FLOAT'],
                'memory_usage' => memory_get_usage(true),
                'cpu_usage' => sys_getloadavg()[0] ?? 0,
                'disk_usage' => disk_free_space('.') / disk_total_space('.') * 100
            ],
            'timestamp' => date('c')
        ];

        return json($health);
    })->name('api.health');

    // API Test Endpoint
    $router->get('/test', function() {
        return json([
            'message' => 'API is working correctly!',
            'method' => 'GET',
            'endpoint' => '/api/test',
            'timestamp' => date('c'),
            'request_id' => uniqid(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown',
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'Unknown'
        ]);
    })->name('api.test');

    // Legacy ping endpoint for compatibility
    $router->get('/ping', function() {
        return json(['pong' => true]);
    })->name('api.ping');
});

// Auto-discover and load module API routes
$modulesPath = BASE_PATH . '/app/modules';

if (is_dir($modulesPath)) {
    $modules = scandir($modulesPath);

    foreach ($modules as $module) {
        if ($module === '.' || $module === '..') {
            continue;
        }

        $moduleApiRoutesFile = $modulesPath . '/' . $module . '/routes/api.php';

        if (file_exists($moduleApiRoutesFile)) {
            require_once $moduleApiRoutesFile;
        }
    }
}

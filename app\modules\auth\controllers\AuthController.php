<?php

namespace App\Modules\Auth\Controllers;

use App\Modules\Auth\Services\AuthService;
use App\Modules\Auth\Services\RegistrationService;
use App\Modules\Auth\Services\LoginService;
use App\Modules\Auth\Services\ValidationService;

/**
 * Authentication Controller
 * Handles authentication related requests
 */
class AuthController
{
    private AuthService $authService;
    private RegistrationService $registrationService;
    private LoginService $loginService;
    private ValidationService $validationService;

    public function __construct()
    {
        $this->authService = new AuthService();
        $this->registrationService = new RegistrationService();
        $this->loginService = new LoginService();
        $this->validationService = new ValidationService();
    }

    /**
     * Show registration form (Step 1)
     */
    public function showRegister()
    {
        // If user is already logged in, redirect
        if ($this->authService->isLoggedIn()) {
            return $this->authService->redirectBasedOnRole();
        }

        $data = [
            'step' => 1,
            'title' => 'Register - JobSpace',
            'roles' => $this->authService->getAvailableRoles()
        ];

        return $this->renderView('register', $data);
    }

    /**
     * Process registration step 1
     */
    public function processRegisterStep1()
    {
        $data = $_POST;
        
        // Validate step 1 data
        $validation = $this->validationService->validateRegistrationStep1($data);
        
        if (!$validation['success']) {
            return $this->jsonResponse([
                'success' => false,
                'errors' => $validation['errors']
            ]);
        }

        // Store step 1 data in session
        $_SESSION['registration_step1'] = $validation['data'];

        return $this->jsonResponse([
            'success' => true,
            'message' => 'Step 1 completed successfully',
            'next_step' => 2
        ]);
    }

    /**
     * Show registration step 2
     */
    public function showRegisterStep2()
    {
        // Check if step 1 is completed
        if (!isset($_SESSION['registration_step1'])) {
            return $this->redirect('/jobspace/register');
        }

        $data = [
            'step' => 2,
            'title' => 'Complete Registration - JobSpace',
            'roles' => $this->authService->getAvailableRoles(),
            'step1_data' => $_SESSION['registration_step1']
        ];

        return $this->renderView('register-step2', $data);
    }

    /**
     * Process registration step 2 and complete registration
     */
    public function processRegisterStep2()
    {
        // Check if step 1 is completed
        if (!isset($_SESSION['registration_step1'])) {
            return $this->jsonResponse([
                'success' => false,
                'message' => 'Please complete step 1 first'
            ]);
        }

        $data = $_POST;
        
        // Validate step 2 data
        $validation = $this->validationService->validateRegistrationStep2($data);
        
        if (!$validation['success']) {
            return $this->jsonResponse([
                'success' => false,
                'errors' => $validation['errors']
            ]);
        }

        // Merge step 1 and step 2 data
        $completeData = array_merge($_SESSION['registration_step1'], $validation['data']);

        // Create user account
        $result = $this->registrationService->createUser($completeData);

        if ($result['success']) {
            // Clear registration session data
            unset($_SESSION['registration_step1']);

            // Auto login if enabled
            if ($this->authService->getConfig('registration.auto_login_after_registration')) {
                $this->loginService->loginUser($result['user']);
            }

            return $this->jsonResponse([
                'success' => true,
                'message' => 'Registration completed successfully',
                'redirect' => $this->authService->getRedirectUrl($result['user']['role'])
            ]);
        } else {
            return $this->jsonResponse([
                'success' => false,
                'message' => $result['message']
            ]);
        }
    }

    /**
     * Show login form
     */
    public function showLogin()
    {
        // If user is already logged in, redirect
        if ($this->authService->isLoggedIn()) {
            return $this->authService->redirectBasedOnRole();
        }

        $data = [
            'title' => 'Login - JobSpace',
            'login_attempts' => $this->loginService->getLoginAttempts()
        ];

        return $this->renderView('login', $data);
    }

    /**
     * Process login
     */
    public function processLogin()
    {
        $data = $_POST;
        
        // Validate login data
        $validation = $this->validationService->validateLogin($data);
        
        if (!$validation['success']) {
            return $this->jsonResponse([
                'success' => false,
                'errors' => $validation['errors']
            ]);
        }

        // Attempt login
        $result = $this->loginService->attemptLogin($validation['data']);

        if ($result['success']) {
            return $this->jsonResponse([
                'success' => true,
                'message' => 'Login successful',
                'redirect' => $result['redirect_url']
            ]);
        } else {
            return $this->jsonResponse([
                'success' => false,
                'message' => $result['message']
            ]);
        }
    }

    /**
     * Logout user
     */
    public function logout()
    {
        $this->loginService->logout();
        
        return $this->redirect('/jobspace/');
    }

    /**
     * Render view
     */
    private function renderView(string $view, array $data = []): string
    {
        extract($data);
        ob_start();
        include BASE_PATH . "/app/modules/auth/views/{$view}.php";
        return ob_get_clean();
    }

    /**
     * JSON response
     */
    private function jsonResponse(array $data): string
    {
        header('Content-Type: application/json');
        return json_encode($data);
    }

    /**
     * Redirect
     */
    private function redirect(string $url): void
    {
        header("Location: {$url}");
        exit;
    }
}

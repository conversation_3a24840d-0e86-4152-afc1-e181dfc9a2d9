-- JobSpace Database Schema - Freelance Marketplace
-- Comprehensive freelancing platform with 30+ features

-- =====================================================
-- FREELANCE MARKETPLACE SYSTEM
-- =====================================================

-- Job categories and skills
CREATE TABLE job_categories (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    parent_id BIGINT UNSIGNED NULL,
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    description TEXT NULL,
    icon VARCHAR(255) NULL,
    color VARCHAR(7) NULL,
    sort_order INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    job_count INT UNSIGNED DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (parent_id) REFERENCES job_categories(id) ON DELETE SET NULL,
    INDEX idx_parent_id (parent_id),
    INDEX idx_slug (slug),
    INDEX idx_active (is_active),
    INDEX idx_sort_order (sort_order),
    INDEX idx_job_count (job_count)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Skills taxonomy
CREATE TABLE skills (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    category_id BIGINT UNSIGNED NULL,
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    description TEXT NULL,
    level ENUM('beginner', 'intermediate', 'advanced', 'expert') NULL,
    is_verified BOOLEAN DEFAULT FALSE,
    usage_count INT UNSIGNED DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (category_id) REFERENCES job_categories(id) ON DELETE SET NULL,
    INDEX idx_category_id (category_id),
    INDEX idx_slug (slug),
    INDEX idx_level (level),
    INDEX idx_verified (is_verified),
    INDEX idx_usage_count (usage_count),
    FULLTEXT idx_name_search (name, description)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Job postings
CREATE TABLE jobs (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    uuid CHAR(36) UNIQUE NOT NULL,
    employer_id BIGINT UNSIGNED NOT NULL,
    category_id BIGINT UNSIGNED NOT NULL,
    title VARCHAR(500) NOT NULL,
    slug VARCHAR(500) UNIQUE NOT NULL,
    description TEXT NOT NULL,
    requirements TEXT NULL,
    deliverables TEXT NULL,
    job_type ENUM('fixed_price', 'hourly', 'milestone_based') NOT NULL,
    experience_level ENUM('entry', 'intermediate', 'expert') NOT NULL,
    budget_type ENUM('fixed', 'range', 'hourly') NOT NULL,
    budget_min DECIMAL(12,2) NULL,
    budget_max DECIMAL(12,2) NULL,
    hourly_rate_min DECIMAL(8,2) NULL,
    hourly_rate_max DECIMAL(8,2) NULL,
    currency VARCHAR(3) DEFAULT 'USD',
    duration ENUM('less_than_1_month', '1_to_3_months', '3_to_6_months', 'more_than_6_months') NULL,
    location_type ENUM('remote', 'onsite', 'hybrid') DEFAULT 'remote',
    location VARCHAR(255) NULL,
    timezone VARCHAR(50) NULL,
    required_skills JSON NOT NULL,
    preferred_skills JSON NULL,
    attachments JSON NULL,
    questions JSON NULL, -- screening questions
    status ENUM('draft', 'published', 'in_progress', 'completed', 'cancelled', 'paused') DEFAULT 'draft',
    visibility ENUM('public', 'private', 'invited_only') DEFAULT 'public',
    featured_until TIMESTAMP NULL,
    urgent_until TIMESTAMP NULL,
    applications_count INT UNSIGNED DEFAULT 0,
    max_applications INT UNSIGNED NULL,
    hired_count INT UNSIGNED DEFAULT 0,
    max_hires INT UNSIGNED DEFAULT 1,
    view_count INT UNSIGNED DEFAULT 0,
    bookmark_count INT UNSIGNED DEFAULT 0,
    expires_at TIMESTAMP NULL,
    published_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (employer_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (category_id) REFERENCES job_categories(id) ON DELETE CASCADE,
    INDEX idx_employer_id (employer_id),
    INDEX idx_category_id (category_id),
    INDEX idx_slug (slug),
    INDEX idx_job_type (job_type),
    INDEX idx_experience_level (experience_level),
    INDEX idx_budget (budget_min, budget_max),
    INDEX idx_location_type (location_type),
    INDEX idx_status (status),
    INDEX idx_visibility (visibility),
    INDEX idx_featured (featured_until),
    INDEX idx_urgent (urgent_until),
    INDEX idx_expires (expires_at),
    INDEX idx_published (published_at),
    INDEX idx_created_at (created_at),
    FULLTEXT idx_search (title, description, requirements)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Job applications/proposals
CREATE TABLE job_applications (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    uuid CHAR(36) UNIQUE NOT NULL,
    job_id BIGINT UNSIGNED NOT NULL,
    freelancer_id BIGINT UNSIGNED NOT NULL,
    cover_letter TEXT NOT NULL,
    proposed_budget DECIMAL(12,2) NULL,
    proposed_timeline VARCHAR(255) NULL,
    hourly_rate DECIMAL(8,2) NULL,
    estimated_hours INT UNSIGNED NULL,
    attachments JSON NULL,
    portfolio_items JSON NULL,
    answers JSON NULL, -- answers to screening questions
    status ENUM('pending', 'shortlisted', 'interviewed', 'hired', 'rejected', 'withdrawn') DEFAULT 'pending',
    employer_notes TEXT NULL,
    interview_scheduled_at TIMESTAMP NULL,
    responded_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (job_id) REFERENCES jobs(id) ON DELETE CASCADE,
    FOREIGN KEY (freelancer_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_job_application (job_id, freelancer_id),
    INDEX idx_job_id (job_id),
    INDEX idx_freelancer_id (freelancer_id),
    INDEX idx_status (status),
    INDEX idx_proposed_budget (proposed_budget),
    INDEX idx_created_at (created_at),
    FULLTEXT idx_cover_letter_search (cover_letter)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Contracts between employers and freelancers
CREATE TABLE contracts (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    uuid CHAR(36) UNIQUE NOT NULL,
    job_id BIGINT UNSIGNED NOT NULL,
    application_id BIGINT UNSIGNED NOT NULL,
    employer_id BIGINT UNSIGNED NOT NULL,
    freelancer_id BIGINT UNSIGNED NOT NULL,
    title VARCHAR(500) NOT NULL,
    description TEXT NOT NULL,
    contract_type ENUM('fixed_price', 'hourly', 'milestone_based') NOT NULL,
    total_amount DECIMAL(12,2) NULL,
    hourly_rate DECIMAL(8,2) NULL,
    estimated_hours INT UNSIGNED NULL,
    currency VARCHAR(3) DEFAULT 'USD',
    start_date DATE NOT NULL,
    end_date DATE NULL,
    status ENUM('pending', 'active', 'paused', 'completed', 'cancelled', 'disputed') DEFAULT 'pending',
    terms_and_conditions TEXT NULL,
    payment_terms TEXT NULL,
    deliverables TEXT NULL,
    milestones JSON NULL,
    total_paid DECIMAL(12,2) DEFAULT 0.00,
    total_hours_logged DECIMAL(8,2) DEFAULT 0.00,
    employer_rating DECIMAL(3,2) NULL,
    freelancer_rating DECIMAL(3,2) NULL,
    employer_feedback TEXT NULL,
    freelancer_feedback TEXT NULL,
    signed_by_employer_at TIMESTAMP NULL,
    signed_by_freelancer_at TIMESTAMP NULL,
    started_at TIMESTAMP NULL,
    completed_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (job_id) REFERENCES jobs(id) ON DELETE CASCADE,
    FOREIGN KEY (application_id) REFERENCES job_applications(id) ON DELETE CASCADE,
    FOREIGN KEY (employer_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (freelancer_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_job_id (job_id),
    INDEX idx_application_id (application_id),
    INDEX idx_employer_id (employer_id),
    INDEX idx_freelancer_id (freelancer_id),
    INDEX idx_status (status),
    INDEX idx_contract_type (contract_type),
    INDEX idx_dates (start_date, end_date),
    INDEX idx_amount (total_amount),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Contract milestones
CREATE TABLE contract_milestones (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    contract_id BIGINT UNSIGNED NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT NULL,
    amount DECIMAL(12,2) NOT NULL,
    due_date DATE NULL,
    status ENUM('pending', 'in_progress', 'submitted', 'approved', 'rejected', 'paid') DEFAULT 'pending',
    deliverables TEXT NULL,
    submission_notes TEXT NULL,
    feedback TEXT NULL,
    submitted_at TIMESTAMP NULL,
    approved_at TIMESTAMP NULL,
    paid_at TIMESTAMP NULL,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (contract_id) REFERENCES contracts(id) ON DELETE CASCADE,
    INDEX idx_contract_id (contract_id),
    INDEX idx_status (status),
    INDEX idx_due_date (due_date),
    INDEX idx_sort_order (sort_order),
    INDEX idx_amount (amount)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Time tracking for hourly contracts
CREATE TABLE time_logs (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    contract_id BIGINT UNSIGNED NOT NULL,
    freelancer_id BIGINT UNSIGNED NOT NULL,
    description TEXT NOT NULL,
    hours DECIMAL(8,2) NOT NULL,
    hourly_rate DECIMAL(8,2) NOT NULL,
    total_amount DECIMAL(12,2) NOT NULL,
    date_worked DATE NOT NULL,
    start_time TIME NULL,
    end_time TIME NULL,
    status ENUM('pending', 'approved', 'rejected', 'paid') DEFAULT 'pending',
    employer_notes TEXT NULL,
    approved_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (contract_id) REFERENCES contracts(id) ON DELETE CASCADE,
    FOREIGN KEY (freelancer_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_contract_id (contract_id),
    INDEX idx_freelancer_id (freelancer_id),
    INDEX idx_status (status),
    INDEX idx_date_worked (date_worked),
    INDEX idx_hours (hours),
    INDEX idx_amount (total_amount)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Freelancer portfolios
CREATE TABLE portfolios (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    uuid CHAR(36) UNIQUE NOT NULL,
    freelancer_id BIGINT UNSIGNED NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    category_id BIGINT UNSIGNED NULL,
    skills JSON NULL,
    images JSON NULL,
    videos JSON NULL,
    live_url VARCHAR(500) NULL,
    github_url VARCHAR(500) NULL,
    project_duration VARCHAR(100) NULL,
    client_name VARCHAR(255) NULL,
    project_value DECIMAL(12,2) NULL,
    is_featured BOOLEAN DEFAULT FALSE,
    view_count INT UNSIGNED DEFAULT 0,
    like_count INT UNSIGNED DEFAULT 0,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (freelancer_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (category_id) REFERENCES job_categories(id) ON DELETE SET NULL,
    INDEX idx_freelancer_id (freelancer_id),
    INDEX idx_category_id (category_id),
    INDEX idx_featured (is_featured),
    INDEX idx_view_count (view_count),
    INDEX idx_sort_order (sort_order),
    FULLTEXT idx_search (title, description)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Reviews and ratings
CREATE TABLE reviews (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    contract_id BIGINT UNSIGNED NOT NULL,
    reviewer_id BIGINT UNSIGNED NOT NULL,
    reviewee_id BIGINT UNSIGNED NOT NULL,
    reviewer_type ENUM('employer', 'freelancer') NOT NULL,
    rating DECIMAL(3,2) NOT NULL,
    title VARCHAR(255) NULL,
    comment TEXT NULL,
    skills_rating DECIMAL(3,2) NULL,
    communication_rating DECIMAL(3,2) NULL,
    quality_rating DECIMAL(3,2) NULL,
    timeliness_rating DECIMAL(3,2) NULL,
    professionalism_rating DECIMAL(3,2) NULL,
    would_recommend BOOLEAN NULL,
    is_public BOOLEAN DEFAULT TRUE,
    response TEXT NULL,
    responded_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (contract_id) REFERENCES contracts(id) ON DELETE CASCADE,
    FOREIGN KEY (reviewer_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (reviewee_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_contract_reviewer (contract_id, reviewer_id),
    INDEX idx_contract_id (contract_id),
    INDEX idx_reviewer_id (reviewer_id),
    INDEX idx_reviewee_id (reviewee_id),
    INDEX idx_reviewer_type (reviewer_type),
    INDEX idx_rating (rating),
    INDEX idx_public (is_public),
    FULLTEXT idx_comment_search (title, comment)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

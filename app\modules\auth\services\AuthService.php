<?php

namespace App\Modules\Auth\Services;

use App\Modules\Auth\Models\UserModel;

/**
 * Authentication Service
 * Core authentication functionality
 */
class AuthService
{
    private array $config;

    public function __construct()
    {
        $this->config = include BASE_PATH . '/app/modules/auth/config/auth.php';
        $this->initializeSession();
    }

    /**
     * Initialize session
     */
    private function initializeSession(): void
    {
        if (session_status() === PHP_SESSION_NONE) {
            session_name($this->config['session_name']);
            session_start();
        }
    }

    /**
     * Check if user is logged in
     */
    public function isLoggedIn(): bool
    {
        return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
    }

    /**
     * Get current user
     */
    public function getCurrentUser(): ?array
    {
        if (!$this->isLoggedIn()) {
            return null;
        }

        return UserModel::findById($_SESSION['user_id']);
    }

    /**
     * Get current user ID
     */
    public function getCurrentUserId(): ?int
    {
        return $_SESSION['user_id'] ?? null;
    }

    /**
     * Get current user role
     */
    public function getCurrentUserRole(): ?string
    {
        $user = $this->getCurrentUser();
        return $user['role'] ?? null;
    }

    /**
     * Check if user has permission
     */
    public function hasPermission(string $permission): bool
    {
        $role = $this->getCurrentUserRole();
        
        if (!$role) {
            return false;
        }

        $roleConfig = $this->config['roles'][$role] ?? null;
        
        if (!$roleConfig) {
            return false;
        }

        // Admin has all permissions
        if (in_array('*', $roleConfig['permissions'])) {
            return true;
        }

        return in_array($permission, $roleConfig['permissions']);
    }

    /**
     * Get available roles for registration
     */
    public function getAvailableRoles(): array
    {
        $roles = [];
        
        foreach ($this->config['roles'] as $key => $role) {
            // Skip admin if max accounts reached
            if ($key === 'admin' && $this->isAdminAccountExists()) {
                continue;
            }
            
            // Only show roles allowed for registration
            if (in_array($key, $this->config['registration']['allowed_roles']) || $key === 'admin') {
                $roles[$key] = [
                    'key' => $key,
                    'name' => $role['name'],
                    'description' => $this->getRoleDescription($key)
                ];
            }
        }

        return $roles;
    }

    /**
     * Check if admin account exists
     */
    private function isAdminAccountExists(): bool
    {
        return UserModel::countByRole('admin') >= ($this->config['roles']['admin']['max_accounts'] ?? 1);
    }

    /**
     * Get role description
     */
    private function getRoleDescription(string $role): string
    {
        $descriptions = [
            'admin' => 'সম্পূর্ণ সিস্টেম নিয়ন্ত্রণ এবং ব্যবস্থাপনা',
            'creator' => 'কুইজ, প্রোডাক্ট, জব এবং বিজ্ঞাপন তৈরি করুন',
            'business' => 'শুধুমাত্র বিজ্ঞাপন তৈরি এবং প্রচার করুন',
            'user' => 'কুইজ খেলুন, কেনাকাটা করুন এবং সোশ্যাল মিডিয়া ব্যবহার করুন'
        ];

        return $descriptions[$role] ?? '';
    }

    /**
     * Get redirect URL based on role
     */
    public function getRedirectUrl(string $role): string
    {
        return $this->config['roles'][$role]['redirect_after_login'] ?? '/jobspace/';
    }

    /**
     * Redirect based on current user role
     */
    public function redirectBasedOnRole(): void
    {
        $role = $this->getCurrentUserRole();
        
        if ($role) {
            $redirectUrl = $this->getRedirectUrl($role);
            header("Location: {$redirectUrl}");
            exit;
        }
        
        header("Location: /jobspace/");
        exit;
    }

    /**
     * Get configuration value
     */
    public function getConfig(string $key, $default = null)
    {
        $keys = explode('.', $key);
        $value = $this->config;

        foreach ($keys as $k) {
            if (!isset($value[$k])) {
                return $default;
            }
            $value = $value[$k];
        }

        return $value;
    }

    /**
     * Validate password strength
     */
    public function validatePassword(string $password): array
    {
        $config = $this->config['password'];
        $errors = [];

        if (strlen($password) < $config['min_length']) {
            $errors[] = "পাসওয়ার্ড কমপক্ষে {$config['min_length']} অক্ষরের হতে হবে";
        }

        if ($config['require_uppercase'] && !preg_match('/[A-Z]/', $password)) {
            $errors[] = "পাসওয়ার্ডে কমপক্ষে একটি বড় হাতের অক্ষর থাকতে হবে";
        }

        if ($config['require_lowercase'] && !preg_match('/[a-z]/', $password)) {
            $errors[] = "পাসওয়ার্ডে কমপক্ষে একটি ছোট হাতের অক্ষর থাকতে হবে";
        }

        if ($config['require_numbers'] && !preg_match('/\d/', $password)) {
            $errors[] = "পাসওয়ার্ডে কমপক্ষে একটি সংখ্যা থাকতে হবে";
        }

        if ($config['require_symbols'] && !preg_match('/[^a-zA-Z\d]/', $password)) {
            $errors[] = "পাসওয়ার্ডে কমপক্ষে একটি বিশেষ চিহ্ন থাকতে হবে";
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }

    /**
     * Hash password
     */
    public function hashPassword(string $password): string
    {
        return password_hash($password, $this->config['password']['hash_algorithm']);
    }

    /**
     * Verify password
     */
    public function verifyPassword(string $password, string $hash): bool
    {
        return password_verify($password, $hash);
    }
}

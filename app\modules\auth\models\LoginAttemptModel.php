<?php

namespace App\Modules\Auth\Models;

/**
 * Login Attempt Model
 * Handles login attempt tracking
 */
class LoginAttemptModel
{
    private static array $attempts = [];

    /**
     * Record login attempt
     */
    public static function record(string $identifier, bool $successful): void
    {
        $attempt = [
            'identifier' => $identifier,
            'successful' => $successful,
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            'timestamp' => time()
        ];

        // In real application, this would be stored in database
        self::$attempts[] = $attempt;
        
        // Log the attempt
        $status = $successful ? 'successful' : 'failed';
        error_log("Login attempt ({$status}): {$identifier} from {$attempt['ip_address']}");
    }

    /**
     * Get recent attempts for identifier
     */
    public static function getRecentAttempts(string $identifier, int $timeWindow): array
    {
        $cutoffTime = time() - $timeWindow;
        $recentAttempts = [];

        foreach (self::$attempts as $attempt) {
            if ($attempt['identifier'] === $identifier && 
                $attempt['timestamp'] >= $cutoffTime && 
                !$attempt['successful']) {
                $recentAttempts[] = $attempt;
            }
        }

        return $recentAttempts;
    }

    /**
     * Clear attempts for identifier
     */
    public static function clearAttempts(string $identifier): void
    {
        self::$attempts = array_filter(self::$attempts, function($attempt) use ($identifier) {
            return $attempt['identifier'] !== $identifier;
        });
    }

    /**
     * Get all attempts for identifier
     */
    public static function getAllAttempts(string $identifier): array
    {
        return array_filter(self::$attempts, function($attempt) use ($identifier) {
            return $attempt['identifier'] === $identifier;
        });
    }

    /**
     * Get failed attempts count
     */
    public static function getFailedAttemptsCount(string $identifier, int $timeWindow): int
    {
        return count(self::getRecentAttempts($identifier, $timeWindow));
    }

    /**
     * Check if identifier is locked
     */
    public static function isLocked(string $identifier, int $maxAttempts, int $timeWindow): bool
    {
        return self::getFailedAttemptsCount($identifier, $timeWindow) >= $maxAttempts;
    }

    /**
     * Get lockout time remaining
     */
    public static function getLockoutTimeRemaining(string $identifier, int $lockoutDuration): int
    {
        $attempts = self::getAllAttempts($identifier);
        
        if (empty($attempts)) {
            return 0;
        }

        // Get the latest failed attempt
        $latestAttempt = end($attempts);
        $lockoutExpiry = $latestAttempt['timestamp'] + $lockoutDuration;
        
        return max(0, $lockoutExpiry - time());
    }

    /**
     * Clean old attempts
     */
    public static function cleanOldAttempts(int $maxAge): void
    {
        $cutoffTime = time() - $maxAge;
        
        self::$attempts = array_filter(self::$attempts, function($attempt) use ($cutoffTime) {
            return $attempt['timestamp'] >= $cutoffTime;
        });
    }

    /**
     * Get statistics
     */
    public static function getStatistics(): array
    {
        $total = count(self::$attempts);
        $successful = count(array_filter(self::$attempts, function($attempt) {
            return $attempt['successful'];
        }));
        $failed = $total - $successful;

        return [
            'total_attempts' => $total,
            'successful_attempts' => $successful,
            'failed_attempts' => $failed,
            'success_rate' => $total > 0 ? round(($successful / $total) * 100, 2) : 0
        ];
    }
}

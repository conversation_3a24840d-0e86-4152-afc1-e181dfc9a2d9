Build "jobspace" with 200+ features using Pure PHP, Tailwind CSS, MariaDB + InnoDB and Composer only. No third-party services except Composer. Implement custom file-based caching. The platform integrates freelancing, social media, e-commerce, and live quiz with 3 roles (Admin/Creator/User). Must handle 50,000+ concurrent users.

### Core Requirements:
1. Architecture & Performance (Designed to Handle 50,000+ Concurrent Users Smoothly)
Backend: Pure PHP 8+, (Custom MVC + Modular Framework)
Database: MariaDB with InnoDB engine ✅ Query Indexing + Prepared Statements for high performance
Frontend: Tailwind CSS
Web Server: Apache and Nginx (both supported; compatible with shared or dedicated hosting)
Autoloading: Composer (PSR-4) for Dependencies
Caching: Custom File-Based Smart Cache with Auto-Invalidation, and OPcache

2. **Database Design**:
Proper normalization of tables for users, transactions, and content
Use InnoDB storage engine to support ACID transactions
Implement table partitioning for very large datasets
Optimize queries with appropriate indexing strategies

⚙️ Platform Roles
Admin: Full system access, moderation, settings
Creator: Create content (jobs, quizzes, products, ad)
User: Consumers, job seekers, shoppers, quiz participants

3. **Key Modules**:
   - User Management (20 features)
   - Quiz System (35 features)
   - Freelancing (30 features)
   - E-commerce (35 features)
   - Social Media (30 features)
   - Wallet/Payment (25 features)
   - Admin Panel (35 features)
   - Additional Systems (20 features)
   - Custom Caching (10 features)

4. **Scalability**:
   - Horizontal scaling for PHP-FPM
   - Database sharding for user data
   - File-based caching at multiple layers
   - Load balancing with Nginx
   - Database replication

5. **Security**:
   - Prepared statements for SQL injection prevention
   - CSRF tokens for form submissions
   - Rate limiting for API endpoints
   - SSL/TLS encryption
   - Input validation and sanitization

### Deliverables:
1. Complete database schema (ERD)
2. PHP class structure for all modules
3. Custom caching implementation
4. Nginx configuration files
5. Security implementation guide
6. Performance optimization checklist
7. Deployment scripts
8. Monitoring setup guide

### Performance Targets:
- <200ms response time for 90% of requests
- 99.9% uptime
- Handle 10,000 concurrent users
- Support 1M+ registered users


ফিড মডিউলের মূল বৈশিষ্ট্যসমূহ ও প্রয়োজনীয় ফিচার
১. কোর ফিড ফিচার (২০টি)
ব্যক্তিগতকৃত কন্টেন্ট ফিড (ইউজারের আচরণ ও পছন্দ অনুযায়ী)
রিয়েল-টাইম কন্টেন্ট আপডেট
কন্টেন্ট ফিল্টারিং (টাইপ, ক্যাটাগরি, জনপ্রিয়তা অনুযায়ী)
বুকমার্ক/সংরক্ষণ সুবিধা
কন্টেন্ট শেয়ারিং
অনুপযুক্ত কন্টেন্ট রিপোর্টিং
কন্টেন্ট রেকমেন্ডেশন ইঞ্জিন
ট্রেন্ডিং টপিক্স সেকশন
অ্যাডভান্সড সার্চ ফাংশন
কন্টেন্ট রেটিং সিস্টেম
কন্টেন্ট ট্যাগিং
কন্টেন্ট ক্যাটাগরাইজেশন
ইউজার অ্যাকটিভিটি ফিড
কন্টেন্ট সাজেশন
কন্টেন্ট হাইলাইট
কন্টেন্ট প্রায়োরিটি
কন্টেন্ট সোর্টিং
কন্টেন্ট আর্কাইভ
কন্টেন্ট সার্চ হিস্টরি
কন্টেন্ট রিলেটেড সাজেশন
২. সোশ্যাল মিডিয়া ফিচার  (শেয়ার, কমেন্ট, লাইক পোস্ট থেকে পয়েন্ট অর্জন)
পোস্ট তৈরি ও শেয়ার
কমেন্ট সিস্টেম
লাইক/রিঅ্যাকশন সিস্টেম
বন্ধু/ফলোয়ার সিস্টেম
প্রোফাইল ভিউ ও কাস্টমাইজেশন
গ্রুপ তৈরি ও জয়েন
পেজ তৈরি ও ফলো
স্টোরি পোস্টিং
মেসেজিং সিস্টেম
নোটিফিকেশন সিস্টেম
প্রাইভেসি সেটিংস
ব্লক/আনব্লক ইউজার
ট্যাগ ইউজার
মেনশন ইউজার
শেয়ার টু সোশ্যাল মিডিয়া
ফটো আপলোড
পোল/কুইজ তৈরি
ইউজার সাজেশন
মিউচুয়াল কানেকশন
বার্থডে রিমাইন্ডার
অনলাইন স্ট্যাটাস
লাস্ট সিন
টাইমলাইন ভিউ
অ্যাক্টিভিটি লগ
ফ্রেন্ড রিকোয়েস্ট
ফলোয়ার রিকোয়েস্ট
গ্রুপ ইনভাইট

৩. কুইজ সিস্টেম ফিচার 
মাল্টিপল কুইজ টাইপ (MCQ, সংক্ষিপ্ত, সত্য/মিথ, ম্যাচিং)
কুইজ ক্যাটাগরি ও সাবক্যাটাগরি
কুইজ ডিফিকাল্টি লেভেল
কুইজ সাবজেক্ট ও টপিক
টাইমড কুইজ
কুইজ রেজাল্ট ও ফিডব্যাক
কুইজ লিডারবোর্ড
কুইজ অ্যাচিভমেন্ট
কুইজ স্ট্রিক
কুইজ হিস্ট্রি
কুইজ প্রোগ্রেস
কুইজ রিকোয়েস্ট
কুইজ চ্যালেঞ্জ
কুইজ টুর্নামেন্ট
কুইজ ব্যাজ
কুইজ পয়েন্ট
কুইজ রিওয়ার্ড
কুইজ শেয়ারিং
কুইজ সাজেশন
কুইজ রেটিং
কুইজ রিভিউ
কুইজ বুকমার্ক
কুইজ ডাউনলোড
কুইজ প্রিন্ট
কুইজ এক্সপোর্ট
কুইজ ইম্পোর্ট
কুইজ টেমপ্লেট
কুইজ কাস্টমাইজেশন
কুইজ র্যান্ডমাইজেশন
কুইজ অটো সাজেশন

৪. গেমিফিকেশন ফিচার
পয়েন্ট/কয়েন সিস্টেম
লেভেল আপ সিস্টেম
অ্যাচিভমেন্ট ব্যাজ
লিডারবোর্ড
স্ট্রিক কাউন্টার
চ্যালেঞ্জ সিস্টেম
রিওয়ার্ড সিস্টেম
ট্রফি সিস্টেম
র‌্যাঙ্কিং সিস্টেম
প্রোগ্রেস বার
এক্সপি সিস্টেম
স্কিল পয়েন্ট
লাকি স্পিন
ডেইলি বোনাস
উইকলি বোনাস
মান্থলি বোনাস
রেফারেল বোনাস
লগইন বোনাস
অ্যাক্টিভিটি বোনাস
এঙ্গেজমেন্ট বোনাস
শেয়ারিং বোনাস

৫. ই-কমার্স ফিচার 
প্রোডাক্ট ভিউ ও সার্চ
প্রোডাক্ট ফিল্টার
প্রোডাক্ট সর্ট
প্রোডাক্ট ক্যাটাগরি
প্রোডাক্ট রেটিং
প্রোডাক্ট রিভিউ
প্রোডাক্ট কম্পেয়ার
প্রোডাক্ট উইশলিস্ট
শপিং কার্ট
চেকআউট সিস্টেম
পেমেন্ট গেটওয়ে
অর্ডার ট্র্যাকিং
অর্ডার হিস্ট্রি
অর্ডার ক্যান্সেলেশন
অর্ডার রিফান্ড
অর্ডার রিটার্ন
অর্ডার ফিডব্যাক
অর্ডার রিভিউ
অর্ডার রেটিং
ডিসকাউন্ট কুপন

৬. ফ্রিল্যান্স মার্কেটপ্লেস ফিচার
জব পোস্টিং
জব সার্চ
জব ফিল্টার
জব ক্যাটাগরি
জব টাইপ
জব বাজেট
জব ডিউরেশন
জব লোকেশন
জব স্কিল
জব এক্সপার্টাইজ
জব প্রপোজাল
জব অ্যাপ্লিকেশন
জব কনট্রাক্ট
জব মাইলস্টোন
জব পেমেন্ট
জব রেটিং
জব রিভিউ
জব ফিডব্যাক
জব পোর্টফোলিও
জব সার্টিফিকেশন
জব ভেরিফিকেশন
জব রেকোমেন্ডেশন
জব অ্যালার্ট
জব নোটিফিকেশন
জব ইনভাইট

৭. স্টাডি গ্রুপ ফিচার 
স্টাডি গ্রুপ তৈরি
স্টাডি গ্রুপ জয়েন
স্টাডি গ্রুপ লিভ
স্টাডি গ্রুপ ডিসকাশন
স্টাডি গ্রুপ রিসোর্স
স্টাডি গ্রুপ নোট
স্টাডি গ্রুপ অ্যাসাইনমেন্ট
স্টাডি গ্রুপ কুইজ
স্টাডি গ্রুপ নোটিফিকেশন
স্টাডি গ্রুপ পারমিশন
স্টাডি গ্রুপ রোল
স্টাডি গ্রুপ মেম্বার
স্টাডি গ্রুপ অ্যাডমিন
স্টাডি গ্রুপ মডারেটর
স্টাডি গ্রুপ ব্লক
স্টাডি গ্রুপ রিপোর্ট

৮. অ্যাডভার্টাইজিং ফিচার 
অ্যাড ডিসপ্লে
অ্যাড ক্লিক
অ্যাড ইম্প্রেশন
অ্যাড কনভারশন
অ্যাড রেভিনিউ
অ্যাড ক্যাম্পেইন
অ্যাড টার্গেটিং
অ্যাড রিটার্গেটিং
অ্যাড অপ্টিমাইজেশন
অ্যাড অ্যানালিটিক্স
অ্যাড রিপোর্ট
অ্যাড বাজেট
অ্যাড বিডিং
অ্যাড অকশন
অ্যাড পারফরম্যান্স
অ্যাড ইফেক্টিভনেস
অ্যাড সিআরও
অ্যাড সিটিআর
অ্যাড কিউআর

১০. অতিরিক্ত ফিচার (১০টি)
মাল্টিল্যাঙ্গুয়াল সাপোর্ট
ডার্ক/লাইট মোড
পুশ নোটিফিকেশন
ইমেল টেমপ্লেট সিস্টেম
এসইও টুলস
সোশ্যাল মিডিয়া ইন্টিগ্রেশন
থার্ড পার্টি এপিআই ইন্টিগ্রেশন
ব্যাকআপ ও রিস্টোর সিস্টেম
ক্যাশে ম্যানেজমেন্ট
সিস্টেম হেলথ মনিটরিং

বিজনেস মডেল ও রাজস্ব উৎস
রাজস্ব উৎস
কুইজ ফি: ব্যবহারকারীরা প্রিমিয়াম কুইজে অংশগ্রহণের জন্য ফি প্রদান করবে
প্রোডাক্ট সেলস কমিশন: ই-কমার্স প্রোডাক্ট বিক্রি থেকে কমিশন
জব পোস্টিং ফি: ফ্রিল্যান্সাররা জব পোস্ট করার জন্য ফি প্রদান করবে
বিজ্ঞাপন রাজস্ব: অ্যাডমিন/ক্রিয়েটরদের দ্বারা তৈরি বিজ্ঞাপন থেকে আয়
প্রিমিয়াম সাবস্ক্রিপশন: ব্যবহারকারীরা মাসিক/বার্ষিক সাবস্ক্রিপশন নিতে পারবে

পেমেন্ট ফ্লো

ইউজার কুইজে জয়েন করে → ব্যালেন্স চেক → পেমেন্ট গেটওয়ে → কুইজ আনলক → কুইজ শুরু
ইউজার প্রোডাক্ট কিনে → ব্যালেন্স চেক → পেমেন্ট গেটওয়ে → অর্ডার কনফার্ম → ডেলিভারি
ইউজার জবে এপ্লাই করে → প্রপোজাল সাবমিট → জব অ্যাওয়ার্ড → কাজ সম্পন্ন → পেমেন্ট
ব্যবহারকারী অভিজ্ঞতা ফ্লো
হোম পেজ ফ্লো

ইউজার হোমে আসে → সব ধরনের কন্টেন্ট দেখে → আগ্রহ অনুযায়ী ফিল্টার করে → কন্টেন্ট কার্ডে ক্লিক করে → ডিটেইল ভিউ দেখে → অ্যাকশন নেয়

ক্যাটাগরি ভিত্তিক ফ্লো

ইউজার নেভিগেশন থেকে কুইজ/সোশ্যাল/ফ্রিল্যান্স/ই-কমার্স সিলেক্ট করে → সেই ক্যাটাগরির কন্টেন্ট দেখে → কন্টেন্ট কার্ডে ক্লিক করে → ডিটেইল ভিউ দেখে → অ্যাকশন নেয়

বিজ্ঞাপন ফ্লো

অ্যাডমিন/ক্রিয়েটর বিজ্ঞাপন তৈরি করে → অ্যাড অনুমোদন → ফিডে নির্দিষ্ট অবস্থানে শো → ইউজার বিজ্ঞাপন দেখে → ক্লিক করে → ডেস্টিনেশনে নিয়ে যায়

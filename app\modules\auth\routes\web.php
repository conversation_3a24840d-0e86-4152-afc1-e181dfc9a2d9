<?php

/**
 * Authentication Routes
 * Web routes for authentication module
 */

use App\Modules\Auth\Controllers\AuthController;

$controller = new AuthController();

// Authentication routes
return [
    // Registration routes
    '/register' => [$controller, 'showRegister'],
    '/register/step1' => [$controller, 'processRegisterStep1'],
    '/register/step2' => [$controller, 'showRegisterStep2'],
    '/register/complete' => [$controller, 'processRegisterStep2'],
    
    // Login routes
    '/login' => [$controller, 'showLogin'],
    '/login/process' => [$controller, 'processLogin'],
    '/logout' => [$controller, 'logout'],
    
    // Password reset routes (to be implemented)
    '/forgot-password' => [$controller, 'showForgotPassword'],
    '/reset-password' => [$controller, 'showResetPassword'],
    '/reset-password/process' => [$controller, 'processResetPassword'],
    
    // Email verification routes (to be implemented)
    '/verify-email' => [$controller, 'verifyEmail'],
    '/resend-verification' => [$controller, 'resendVerification'],
];

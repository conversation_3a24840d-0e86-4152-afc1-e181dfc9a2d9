<?php
/**
 * Ultra-Attractive Hero Section - JobSpace Quiz Platform
 * Mind-blowing, conversion-optimized, visually stunning hero section
 * Designed to hypnotize users and drive immediate action
 */
// Dynamic data for hero section
$heroData = [
    'title' => 'জ্ঞান দিয়ে অর্থ উপার্জন করুন, প্রতিদিন!',
    'subtitle' => 'JobSpace-এ যোগ দিন - যেখানে জ্ঞান পুরস্কারের সাথে মিলেমিশে একাকার! আকর্ষণীয় কুইজ খেলুন, প্রকৃত অর্থ এবং কয়েন উপার্জন করুন, রোমাঞ্চকর চ্যালেঞ্জে অংশগ্রহণ করুন। আজই আপনার উপার্জনের যাত্রা শুরু করুন এবং আনন্দ উপভোগ করুন!',
    'stats' => [
        ['number' => '৳৫,০০,০০০+', 'label' => 'মোট প্রদান', 'icon' => 'fas fa-money-bill-wave'],
        ['number' => '২৫,০০০+', 'label' => 'সক্রিয় ব্যবহারকারী', 'icon' => 'fas fa-users'],
        ['number' => '২,৫০,০০০+', 'label' => 'সম্পন্ন কুইজ', 'icon' => 'fas fa-brain'],
        ['number' => '৯৯.৮%', 'label' => 'ব্যবহারকারী সন্তুষ্টি', 'icon' => 'fas fa-star']
    ],
    'how_it_works' => [
        [
            'step' => 1,
            'title' => 'নিবন্ধন করুন ও অ্যাকাউন্ট তৈরি করুন',
            'description' => 'বিনামূল্যে সাইন আপ করুন এবং ২ মিনিটের মধ্যে আপনার অ্যাকাউন্ট তৈরি করুন',
            'icon' => 'fas fa-user-plus'
        ],
        [
            'step' => 2,
            'title' => 'কুইজ খেলুন ও উপার্জন করুন',
            'description' => 'সঠিকভাবে প্রশ্নের উত্তর দিন এবং প্রকৃত অর্থ ও কয়েন উপার্জন করুন',
            'icon' => 'fas fa-brain'
        ],
        [
            'step' => 3,
            'title' => 'আপনার উপার্জন তুলুন',
            'description' => 'আপনার উপার্জন তাত্ক্ষণিকভাবে আপনার মোবাইল ওয়ালেট বা ব্যাংক অ্যাকাউন্টে উত্তোলন করুন',
            'icon' => 'fas fa-money-check-alt'
        ]
    ],
    'testimonials' => [
        [
            'name' => 'রহিম আহমেদ',
            'location' => 'ঢাকা',
            'text' => 'আমি গত মাসে শুধু অবসর সময়ে কুইজ খেলে ৳১২,০০০ উপার্জন করেছি!',
            'earning' => '৳১২,০০০',
            'avatar' => 'https://randomuser.me/api/portraits/men/1.jpg'
        ],
        [
            'name' => 'ফাতেমা বেগম',
            'location' => 'চট্টগ্রাম',
            'text' => 'JobSpace আমার জীবন বদলে দিয়েছে। এখন আমি প্রতিদিন নতুন কিছু শেখার সাথে সাথে আয় করছি।',
            'earning' => '৳৮,৫০০',
            'avatar' => 'https://randomuser.me/api/portraits/women/1.jpg'
        ],
        [
            'name' => 'করিম উদ্দিন',
            'location' => 'সিলেট',
            'text' => 'বাংলাদেশের সেরা কুইজ প্ল্যাটফর্ম। পেমেন্ট সবসময় ঠিক সময়ে পাই!',
            'earning' => '৳১৫,৩০০',
            'avatar' => 'https://randomuser.me/api/portraits/men/2.jpg'
        ]
    ],
    'recent_winners' => [
        ['name' => 'সাজিদ হাসান', 'amount' => '৳২,৫০০', 'time' => '২ ঘন্টা আগে'],
        ['name' => 'নাজমুন নাহার', 'amount' => '৳১,৮০০', 'time' => '৪ ঘন্টা আগে'],
        ['name' => 'তানভীর আহমেদ', 'amount' => '৳৩,২০০', 'time' => '৬ ঘন্টা আগে'],
        ['name' => 'শারমিন আক্তার', 'amount' => '৳১,৫০০', 'time' => '৮ ঘন্টা আগে']
    ],
    'live_stats' => [
        ['number' => '১২৪', 'label' => 'অনলাইন ব্যবহারকারী', 'icon' => 'fas fa-circle text-green-400'],
        ['number' => '৪২', 'label' => 'নতুন বিজয়ী', 'icon' => 'fas fa-trophy text-yellow-400'],
        ['number' => '৳৮,৭৫০', 'label' => 'আজকের পুরস্কার', 'icon' => 'fas fa-gift text-purple-400']
    ]
];
?>

<!-- Ultra-Attractive Hero Section with Mind-Blowing Design -->
<section class="relative min-h-screen bg-gradient-to-br from-indigo-950 via-purple-900 to-pink-800 overflow-hidden">
    <!-- Animated Background Elements -->
    <div class="absolute inset-0 z-0">
        <div class="floating-elements">
            <div class="absolute top-1/4 left-1/4 w-96 h-96 bg-purple-600 rounded-full mix-blend-multiply filter blur-3xl opacity-20 animate-blob"></div>
            <div class="absolute top-1/3 right-1/4 w-96 h-96 bg-yellow-500 rounded-full mix-blend-multiply filter blur-3xl opacity-20 animate-blob animation-delay-2000"></div>
            <div class="absolute bottom-1/4 left-1/3 w-96 h-96 bg-pink-500 rounded-full mix-blend-multiply filter blur-3xl opacity-20 animate-blob animation-delay-4000"></div>
            
            <!-- Floating Icons -->
            <i class="fas fa-coins absolute top-1/5 left-1/5 text-yellow-400 opacity-30 text-5xl animate-float"></i>
            <i class="fas fa-brain absolute top-1/3 right-1/5 text-blue-300 opacity-30 text-4xl animate-float-reverse"></i>
            <i class="fas fa-trophy absolute bottom-1/4 left-1/4 text-yellow-400 opacity-30 text-6xl animate-float"></i>
            <i class="fas fa-star absolute bottom-1/3 right-1/4 text-white opacity-30 text-4xl animate-float-reverse"></i>
            <i class="fas fa-question-circle absolute top-1/2 left-1/2 text-white opacity-20 text-7xl animate-pulse"></i>
            
            <!-- Grid Pattern -->
            <div class="absolute inset-0 bg-grid-pattern opacity-10"></div>
        </div>
    </div>
    
    <!-- Live Stats Bar -->
    <div class="absolute top-2 left-0 right-0 z-20">
        <div class="container mx-auto px-4">
            <div class="bg-black/40 backdrop-blur-lg rounded-2xl py-3 px-6 flex items-center justify-between">
                <div class="flex items-center space-x-6">
                    <span class="bg-red-500 text-white text-xs font-bold px-3 py-1 rounded-full flex items-center animate-pulse">
                        <span class="flex h-2 w-2 relative mr-2">
                            <span class="animate-ping absolute inline-flex h-full w-full rounded-full bg-white opacity-75"></span>
                            <span class="relative inline-flex rounded-full h-2 w-2 bg-white"></span>
                        </span>
                        LIVE
                    </span>
                    <?php foreach ($heroData['live_stats'] as $stat): ?>
                    <div class="flex items-center text-white">
                        <i class="<?= $stat['icon'] ?> mr-2"></i>
                        <span class="font-bold"><?= htmlspecialchars($stat['number']) ?></span>
                        <span class="text-sm text-gray-300 ml-1"><?= htmlspecialchars($stat['label']) ?></span>
                    </div>
                    <?php endforeach; ?>
                </div>
                
                <!-- Real-time Clock -->
                <div class="text-white text-sm font-medium">
                    <i class="far fa-clock mr-2"></i>
                    <span id="currentTime"></span>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Live Winner Ticker -->
    <div class="absolute top-16 left-0 right-0 z-20">
        <div class="container mx-auto px-4">
            <div class="bg-gradient-to-r from-yellow-500/20 to-orange-500/20 backdrop-blur-md rounded-full py-3 px-6 flex items-center border border-yellow-400/30">
                <span class="bg-gradient-to-r from-yellow-400 to-orange-500 text-gray-900 text-xs font-bold px-3 py-1 rounded-full mr-4 flex items-center">
                    <i class="fas fa-fire mr-2"></i> হট উইনার্স
                </span>
                <div class="flex-1 overflow-hidden">
                    <div class="ticker-wrap">
                        <div class="ticker-move">
                            <?php foreach ($heroData['recent_winners'] as $index => $winner): ?>
                                <div class="ticker-item flex items-center">
                                    <i class="fas fa-trophy text-yellow-400 mr-2"></i>
                                    <span class="text-white font-semibold"><?= htmlspecialchars($winner['name']) ?></span>
                                    <span class="text-green-400 font-bold mx-2"><?= htmlspecialchars($winner['amount']) ?></span>
                                    <span class="text-gray-300 text-sm">জিতেছেন <?= htmlspecialchars($winner['time']) ?></span>
                                    <?php if ($index < count($heroData['recent_winners']) - 1): ?>
                                        <span class="mx-3 text-gray-400">•</span>
                                    <?php endif; ?>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="container mx-auto px-4 py-24 relative z-10">
        <div class="flex flex-col lg:flex-row items-center min-h-screen pt-16">
            <!-- Left Content -->
            <div class="lg:w-1/2 text-white mb-16 lg:mb-0">
                <!-- Badge -->
                <div class="mb-8 flex items-center">
                    <span class="inline-block bg-gradient-to-r from-yellow-400 to-orange-500 text-gray-900 px-6 py-3 rounded-full font-bold text-lg shadow-xl transform hover:scale-105 transition-transform">
                        <i class="fas fa-fire mr-3"></i> বাংলাদেশের #1 উপার্জন প্ল্যাটফর্ম
                    </span>
                    <span class="ml-6 bg-gradient-to-r from-green-500 to-emerald-600 text-white px-4 py-2 rounded-full text-sm font-bold animate-pulse flex items-center">
                        <i class="fas fa-circle text-green-300 mr-2"></i> সক্রিয় ব্যবহারকারী: ২৫,০০০+
                    </span>
                </div>
                
                <!-- Main Heading with Glitch Effect -->
                <div class="relative mb-8">
                    <h1 class="text-5xl md:text-7xl font-black mb-6 leading-tight glitch-text" data-text="<?= htmlspecialchars($heroData['title']) ?>">
                        <?= htmlspecialchars($heroData['title']) ?>
                    </h1>
                    <div class="absolute -bottom-4 left-0 w-32 h-1 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full"></div>
                </div>
                
                <!-- Subtitle -->
                <p class="text-xl md:text-2xl mb-10 text-blue-100 leading-relaxed max-w-2xl">
                    <?= htmlspecialchars($heroData['subtitle']) ?>
                </p>
                
                <!-- How It Works with Enhanced Design -->
                <div class="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm rounded-3xl p-8 mb-12 border border-white/20 shadow-2xl">
                    <div class="flex items-center mb-8">
                        <div class="bg-gradient-to-r from-yellow-400 to-orange-500 w-12 h-12 rounded-full flex items-center justify-center mr-4">
                            <i class="fas fa-lightbulb text-gray-900 text-xl"></i>
                        </div>
                        <h3 class="text-2xl font-bold text-white">কিভাবে কাজ করে</h3>
                    </div>
                    <div class="space-y-6">
                        <?php foreach ($heroData['how_it_works'] as $step): ?>
                        <div class="flex items-start group">
                            <div class="bg-gradient-to-br from-yellow-400 to-orange-500 text-gray-900 rounded-full w-12 h-12 flex items-center justify-center font-bold mr-5 flex-shrink-0 text-xl group-hover:scale-110 transition-transform shadow-lg">
                                <?= $step['step'] ?>
                            </div>
                            <div class="pt-1">
                                <h4 class="font-semibold text-white text-xl mb-2 group-hover:text-yellow-300 transition-colors"><?= $step['title'] ?></h4>
                                <p class="text-blue-100 text-base"><?= $step['description'] ?></p>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                
                <!-- CTA Buttons with Enhanced Design -->
                <div class="flex flex-wrap gap-6 mb-16">
                    <a href="/register" class="group relative overflow-hidden bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-300 hover:to-orange-400 text-gray-900 font-bold py-5 px-10 rounded-2xl transition-all transform hover:scale-105 hover:shadow-2xl text-xl">
                        <span class="relative z-10 flex items-center">
                            <i class="fas fa-rocket mr-3 group-hover:animate-bounce"></i>
                            <span>ফ্রি শুরু করুন</span>
                        </span>
                        <div class="absolute inset-0 bg-gradient-to-r from-yellow-500 to-orange-600 opacity-0 group-hover:opacity-100 transition-opacity"></div>
                    </a>
                    <a href="/about" class="group bg-transparent hover:bg-white/10 text-white font-bold py-5 px-10 rounded-2xl border-2 border-white/30 hover:border-yellow-400 transition-all transform hover:scale-105 text-xl">
                        <i class="fas fa-play-circle mr-3 group-hover:animate-pulse"></i>
                        <span>ডেমো দেখুন</span>
                    </a>
                </div>
                
                <!-- Stats with Enhanced Design -->
                <div class="grid grid-cols-2 md:grid-cols-4 gap-6">
                    <?php foreach ($heroData['stats'] as $stat): ?>
                    <div class="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm rounded-2xl p-6 text-center border border-white/20 hover:bg-white/15 transition-all transform hover:-translate-y-2 hover:shadow-xl">
                        <i class="<?= $stat['icon'] ?> text-yellow-400 text-4xl mb-4"></i>
                        <div class="text-3xl font-black mb-2"><?= htmlspecialchars($stat['number']) ?></div>
                        <div class="text-sm text-blue-100"><?= htmlspecialchars($stat['label']) ?></div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
            
            <!-- Right Content - Ultra-Enhanced Quiz Demo & Testimonials -->
            <div class="lg:w-1/2 lg:pl-16">
                <!-- Ultra-Enhanced Quiz Demo Card -->
                <div class="bg-white rounded-3xl shadow-2xl overflow-hidden mb-10 transform hover:scale-[1.02] transition-transform duration-500 border-4 border-white/20">
                    <!-- Demo Badge with Pulse Effect -->
                    <div class="absolute top-6 right-6 z-20">
                        <span class="bg-gradient-to-r from-green-500 to-emerald-600 text-white px-5 py-2 rounded-full text-sm font-bold shadow-xl flex items-center animate-pulse">
                            <span class="flex h-3 w-3 relative mr-2">
                                <span class="animate-ping absolute inline-flex h-full w-full rounded-full bg-white opacity-75"></span>
                                <span class="relative inline-flex rounded-full h-3 w-3 bg-white"></span>
                            </span>
                            লাইভ ডেমো
                        </span>
                    </div>
                    
                    <!-- Ultra-Enhanced Balance Display -->
                    <div class="bg-gradient-to-r from-indigo-700 to-purple-800 p-8 text-white relative overflow-hidden">
                        <div class="absolute top-0 right-0 w-40 h-40 bg-white/10 rounded-full -mr-20 -mt-20"></div>
                        <div class="absolute bottom-0 left-0 w-32 h-32 bg-white/10 rounded-full -ml-16 -mb-16"></div>
                        
                        <div class="flex justify-between items-center mb-6 relative z-10">
                            <h3 class="text-3xl font-bold">কুইজ ডেমো</h3>
                            <div class="text-sm bg-white/20 px-4 py-2 rounded-full">কিভাবে কাজ করে দেখুন!</div>
                        </div>
                        
                        <div class="flex justify-between gap-6 relative z-10">
                            <div class="bg-gradient-to-br from-white/20 to-white/10 backdrop-blur-sm rounded-2xl p-6 flex-1 border border-white/30 shadow-lg">
                                <div class="flex items-center mb-3">
                                    <i class="fas fa-wallet text-yellow-400 mr-3 text-xl"></i>
                                    <div class="text-sm opacity-90">আপনার ব্যালেন্স</div>
                                </div>
                                <div class="text-4xl font-black flex items-center">
                                    ৳<span id="balanceAmount">1,250</span>
                                    <span class="ml-3 text-green-400 text-sm animate-bounce">▲</span>
                                </div>
                            </div>
                            <div class="bg-gradient-to-br from-white/20 to-white/10 backdrop-blur-sm rounded-2xl p-6 flex-1 border border-white/30 shadow-lg">
                                <div class="flex items-center mb-3">
                                    <i class="fas fa-coins text-yellow-400 mr-3 text-xl"></i>
                                    <div class="text-sm opacity-90">কয়েন</div>
                                </div>
                                <div class="text-4xl font-black flex items-center">
                                    <span id="coinAmount">850</span>
                                    <i class="fas fa-coins text-yellow-400 ml-3"></i>
                                    <span class="ml-3 text-green-400 text-sm animate-bounce">▲</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Ultra-Enhanced Quiz Demo Container -->
                    <div class="p-8">
                        <div id="quizDemo" class="quiz-demo-container">
                            <!-- Question Display -->
                            <div class="quiz-question mb-8">
                                <div class="flex justify-between items-center mb-6">
                                    <span class="bg-gradient-to-r from-blue-100 to-indigo-100 text-blue-800 px-5 py-2 rounded-full text-sm font-bold shadow-md">
                                        প্রশ্ন <span id="questionNumber">1</span>/5
                                    </span>
                                    <div class="quiz-timer flex items-center bg-gradient-to-r from-orange-50 to-red-50 px-5 py-2 rounded-full shadow-md">
                                        <i class="fas fa-clock text-orange-500 mr-2"></i>
                                        <span id="timer" class="font-black text-xl text-orange-600">15</span>
                                        <span class="text-sm text-orange-500 ml-1">সেকেন্ড</span>
                                    </div>
                                </div>
                                
                                <h6 class="text-gray-800 font-black text-2xl mb-8" id="questionText">বাংলাদেশের রাজধানী কোনটি?</h6>
                                
                                <!-- Answer Options -->
                                <div class="quiz-options space-y-4">
                                    <button class="quiz-option w-full text-left p-5 rounded-2xl border-2 border-gray-200 hover:border-blue-400 hover:bg-blue-50 transition-all group" data-answer="wrong">
                                        <span class="font-black mr-3 text-xl text-gray-600 group-hover:text-blue-600">A.</span> 
                                        <span class="text-gray-700 group-hover:text-blue-800 text-lg">চট্টগ্রাম</span>
                                    </button>
                                    <button class="quiz-option w-full text-left p-5 rounded-2xl border-2 border-gray-200 hover:border-blue-400 hover:bg-blue-50 transition-all group" data-answer="correct">
                                        <span class="font-black mr-3 text-xl text-gray-600 group-hover:text-blue-600">B.</span> 
                                        <span class="text-gray-700 group-hover:text-blue-800 text-lg">ঢাকা</span>
                                    </button>
                                    <button class="quiz-option w-full text-left p-5 rounded-2xl border-2 border-gray-200 hover:border-blue-400 hover:bg-blue-50 transition-all group" data-answer="wrong">
                                        <span class="font-black mr-3 text-xl text-gray-600 group-hover:text-blue-600">C.</span> 
                                        <span class="text-gray-700 group-hover:text-blue-800 text-lg">সিলেট</span>
                                    </button>
                                    <button class="quiz-option w-full text-left p-5 rounded-2xl border-2 border-gray-200 hover:border-blue-400 hover:bg-blue-50 transition-all group" data-answer="wrong">
                                        <span class="font-black mr-3 text-xl text-gray-600 group-hover:text-blue-600">D.</span> 
                                        <span class="text-gray-700 group-hover:text-blue-800 text-lg">রাজশাহী</span>
                                    </button>
                                </div>
                            </div>
                            
                            <!-- Ultra-Enhanced Earning Animation -->
                            <div id="earningAnimation" class="earning-animation text-center mb-6" style="display: none;">
                                <div class="bg-gradient-to-r from-green-50 to-emerald-50 border-2 border-green-200 text-green-800 p-6 rounded-2xl shadow-xl transform scale-0 animate-scale-in">
                                    <div class="flex justify-center mb-4">
                                        <div class="bg-green-500 w-20 h-20 rounded-full flex items-center justify-center">
                                            <i class="fas fa-check-circle text-white text-4xl"></i>
                                        </div>
                                    </div>
                                    <h6 class="font-black text-2xl mb-3">সঠিক উত্তর! 🎉</h6>
                                    <p class="mb-0 text-xl">আপনি পেয়েছেন: <span class="font-black text-green-700">+৳50 + 25 Coins</span></p>
                                </div>
                            </div>
                            
                            <!-- Ultra-Enhanced Progress Bar -->
                            <div class="mb-8">
                                <div class="flex justify-between text-sm text-gray-600 mb-3">
                                    <span class="font-bold">অগ্রগতি</span>
                                    <span id="progressText" class="font-black text-blue-600">20%</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-4 overflow-hidden">
                                    <div class="bg-gradient-to-r from-green-500 to-emerald-600 h-4 rounded-full transition-all duration-700 ease-out" id="progressBar" style="width: 20%"></div>
                                </div>
                            </div>
                            
                            <!-- Action Buttons -->
                            <div class="text-center">
                                <button id="startQuizDemo" class="group relative overflow-hidden bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white font-bold py-4 px-10 rounded-2xl transition-all transform hover:scale-105 hover:shadow-xl text-xl">
                                    <span class="relative z-10 flex items-center">
                                        <i class="fas fa-play mr-3 group-hover:animate-pulse"></i>
                                        <span>কুইজ ডেমো শুরু করুন</span>
                                    </span>
                                </button>
                                <button id="nextQuestion" class="group relative overflow-hidden bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white font-bold py-4 px-10 rounded-2xl transition-all transform hover:scale-105 hover:shadow-xl text-xl" style="display: none;">
                                    <span class="relative z-10 flex items-center">
                                        <span>পরবর্তী প্রশ্ন</span>
                                        <i class="fas fa-arrow-right ml-3 group-hover:translate-x-1 transition-transform"></i>
                                    </span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Ultra-Enhanced Testimonials -->
                <div class="bg-white rounded-3xl shadow-2xl p-8 border-4 border-white/20">
                    <div class="flex items-center mb-8">
                        <div class="bg-gradient-to-r from-yellow-400 to-orange-500 w-12 h-12 rounded-full flex items-center justify-center mr-4">
                            <i class="fas fa-star text-gray-900 text-xl"></i>
                        </div>
                        <h3 class="text-3xl font-bold text-gray-800">সফলতার গল্প</h3>
                    </div>
                    <div class="space-y-8">
                        <?php foreach ($heroData['testimonials'] as $testimonial): ?>
                        <div class="group">
                            <div class="bg-gradient-to-br from-gray-50 to-white p-6 rounded-2xl border border-gray-200 shadow-md hover:shadow-xl transition-all transform hover:-translate-y-1">
                                <div class="flex items-start">
                                    <img src="<?= htmlspecialchars($testimonial['avatar']) ?>" alt="<?= htmlspecialchars($testimonial['name']) ?>" class="w-16 h-16 rounded-full object-cover mr-5 border-3 border-white shadow-lg">
                                    <div class="flex-1">
                                        <p class="text-gray-600 italic mb-4 text-xl">"<?= htmlspecialchars($testimonial['text']) ?>"</p>
                                        <div class="flex justify-between items-center">
                                            <div>
                                                <div class="font-black text-gray-800 text-xl"><?= htmlspecialchars($testimonial['name']) ?></div>
                                                <div class="text-sm text-gray-500 flex items-center">
                                                    <i class="fas fa-map-marker-alt mr-2"></i>
                                                    <?= htmlspecialchars($testimonial['location']) ?>
                                                </div>
                                            </div>
                                            <div class="bg-gradient-to-r from-green-500 to-emerald-600 text-white font-black px-5 py-3 rounded-full shadow-lg group-hover:scale-105 transition-transform text-lg">
                                                <?= htmlspecialchars($testimonial['earning']) ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                    
                    <!-- Ultra-Enhanced Trust Indicators -->
                    <div class="mt-10 pt-8 border-t border-gray-200">
                        <div class="flex flex-wrap justify-center gap-6">
                            <div class="flex items-center text-gray-600 bg-gray-50 px-4 py-2 rounded-full">
                                <i class="fas fa-shield-alt text-green-500 mr-2 text-xl"></i>
                                <span class="font-medium">100% সুরক্ষিত পেমেন্ট</span>
                            </div>
                            <div class="flex items-center text-gray-600 bg-gray-50 px-4 py-2 rounded-full">
                                <i class="fas fa-lock text-blue-500 mr-2 text-xl"></i>
                                <span class="font-medium">ব্যক্তিগত তথ্য সুরক্ষিত</span>
                            </div>
                            <div class="flex items-center text-gray-600 bg-gray-50 px-4 py-2 rounded-full">
                                <i class="fas fa-headset text-purple-500 mr-2 text-xl"></i>
                                <span class="font-medium">24/7 সাপোর্ট</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Ultra-Enhanced Wave Separator -->
    <div class="absolute bottom-0 left-0 right-0">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 120" class="w-full h-24 md:h-32">
            <path fill="#ffffff" fill-opacity="1" d="M0,64L48,80C96,96,192,128,288,128C384,128,480,96,576,85.3C672,75,768,85,864,90.7C960,96,1056,96,1152,85.3C1248,75,1344,53,1392,42.7L1440,32L1440,120L1392,120C1344,120,1248,120,1152,120C1056,120,960,120,864,120C768,120,672,120,576,120C480,120,384,120,288,120C192,120,96,120,48,120L0,120Z"></path>
        </svg>
    </div>
</section>

<!-- Ultra-Enhanced Custom Styles -->
<style>
@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}
@keyframes float-reverse {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(-180deg); }
}
@keyframes pulse {
    0%, 100% { opacity: 0.5; }
    50% { opacity: 0.8; }
}
@keyframes blob {
    0% { transform: translate(0px, 0px) scale(1); }
    33% { transform: translate(30px, -50px) scale(1.1); }
    66% { transform: translate(-20px, 20px) scale(0.9); }
    100% { transform: translate(0px, 0px) scale(1); }
}
@keyframes scale-in {
    0% { transform: scale(0); opacity: 0; }
    70% { transform: scale(1.1); opacity: 1; }
    100% { transform: scale(1); opacity: 1; }
}
@keyframes glitch {
    0% { text-shadow: 0.05em 0 0 rgba(255, 0, 0, 0.75), -0.05em -0.025em 0 rgba(0, 255, 0, 0.75), -0.025em 0.05em 0 rgba(0, 0, 255, 0.75); }
    14% { text-shadow: 0.05em 0 0 rgba(255, 0, 0, 0.75), -0.05em -0.025em 0 rgba(0, 255, 0, 0.75), -0.025em 0.05em 0 rgba(0, 0, 255, 0.75); }
    15% { text-shadow: -0.05em -0.025em 0 rgba(255, 0, 0, 0.75), 0.025em 0.025em 0 rgba(0, 255, 0, 0.75), -0.05em -0.05em 0 rgba(0, 0, 255, 0.75); }
    49% { text-shadow: -0.05em -0.025em 0 rgba(255, 0, 0, 0.75), 0.025em 0.025em 0 rgba(0, 255, 0, 0.75), -0.05em -0.05em 0 rgba(0, 0, 255, 0.75); }
    50% { text-shadow: 0.025em 0.05em 0 rgba(255, 0, 0, 0.75), 0.05em 0 0 rgba(0, 255, 0, 0.75), 0 -0.05em 0 rgba(0, 0, 255, 0.75); }
    99% { text-shadow: 0.025em 0.05em 0 rgba(255, 0, 0, 0.75), 0.05em 0 0 rgba(0, 255, 0, 0.75), 0 -0.05em 0 rgba(0, 0, 255, 0.75); }
    100% { text-shadow: -0.025em 0 0 rgba(255, 0, 0, 0.75), -0.025em -0.025em 0 rgba(0, 255, 0, 0.75), -0.025em -0.05em 0 rgba(0, 0, 255, 0.75); }
}
.animate-float {
    animation: float 6s ease-in-out infinite;
}
.animate-float-reverse {
    animation: float-reverse 8s ease-in-out infinite;
}
.animate-pulse {
    animation: pulse 2s ease-in-out infinite;
}
.animate-blob {
    animation: blob 7s infinite;
}
.animation-delay-2000 {
    animation-delay: 2s;
}
.animation-delay-4000 {
    animation-delay: 4s;
}
.animate-scale-in {
    animation: scale-in 0.5s ease-out forwards;
}
.glitch-text {
    position: relative;
    color: white;
    font-size: 7vw;
    font-weight: 900;
    text-transform: uppercase;
    position: relative;
    text-shadow: 0.05em 0 0 rgba(255, 0, 0, 0.75),
                 -0.05em -0.025em 0 rgba(0, 255, 0, 0.75),
                 -0.025em 0.05em 0 rgba(0, 0, 255, 0.75);
    animation: glitch 500ms infinite;
}
.glitch-text::before,
.glitch-text::after {
    content: attr(data-text);
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}
.glitch-text::before {
    animation: glitch-1 500ms infinite;
    color: #00ffff;
    z-index: -1;
}
.glitch-text::after {
    animation: glitch-2 500ms infinite;
    color: #ff00ff;
    z-index: -2;
}
@keyframes glitch-1 {
    0% { clip: rect(44px, 450px, 56px, 0); }
    5% { clip: rect(12px, 450px, 59px, 0); }
    10% { clip: rect(73px, 450px, 27px, 0); }
    15% { clip: rect(20px, 450px, 60px, 0); }
    20% { clip: rect(26px, 450px, 83px, 0); }
    25% { clip: rect(73px, 450px, 73px, 0); }
    30% { clip: rect(67px, 450px, 61px, 0); }
    35% { clip: rect(14px, 450px, 79px, 0); }
    40% { clip: rect(20px, 450px, 34px, 0); }
    45% { clip: rect(65px, 450px, 54px, 0); }
    50% { clip: rect(92px, 450px, 20px, 0); }
    55% { clip: rect(20px, 450px, 16px, 0); }
    60% { clip: rect(84px, 450px, 10px, 0); }
    65% { clip: rect(79px, 450px, 88px, 0); }
    70% { clip: rect(17px, 450px, 32px, 0); }
    75% { clip: rect(70px, 450px, 85px, 0); }
    80% { clip: rect(67px, 450px, 40px, 0); }
    85% { clip: rect(89px, 450px, 69px, 0); }
    90% { clip: rect(20px, 450px, 51px, 0); }
    95% { clip: rect(24px, 450px, 14px, 0); }
    100% { clip: rect(13px, 450px, 45px, 0); }
}
@keyframes glitch-2 {
    0% { clip: rect(65px, 450px, 119px, 0); }
    5% { clip: rect(29px, 450px, 15px, 0); }
    10% { clip: rect(93px, 450px, 98px, 0); }
    15% { clip: rect(37px, 450px, 22px, 0); }
    20% { clip: rect(78px, 450px, 51px, 0); }
    25% { clip: rect(55px, 450px, 89px, 0); }
    30% { clip: rect(96px, 450px, 27px, 0); }
    35% { clip: rect(31px, 450px, 17px, 0); }
    40% { clip: rect(14px, 450px, 16px, 0); }
    45% { clip: rect(25px, 450px, 58px, 0); }
    50% { clip: rect(66px, 450px, 83px, 0); }
    55% { clip: rect(54px, 450px, 81px, 0); }
    60% { clip: rect(94px, 450px, 53px, 0); }
    65% { clip: rect(11px, 450px, 84px, 0); }
    70% { clip: rect(81px, 450px, 105px, 0); }
    75% { clip: rect(82px, 450px, 33px, 0); }
    80% { clip: rect(20px, 450px, 8px, 0); }
    85% { clip: rect(26px, 450px, 90px, 0); }
    90% { clip: rect(57px, 450px, 70px, 0); }
    95% { clip: rect(46px, 450px, 88px, 0); }
    100% { clip: rect(66px, 450px, 105px, 0); }
}
.bg-grid-pattern {
    background-image: linear-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px),
                      linear-gradient(90deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
    background-size: 20px 20px;
}
.quiz-option.correct {
    background-color: #10B981 !important;
    border-color: #10B981 !important;
    color: white !important;
    transform: scale(1.02);
}
.quiz-option.wrong {
    background-color: #EF4444 !important;
    border-color: #EF4444 !important;
    color: white !important;
}
/* Ticker Styles */
.ticker-wrap {
    overflow-x: hidden;
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
}
.ticker-move {
    display: flex;
    animation: ticker 30s linear infinite;
}
.ticker-item {
    flex-shrink: 0;
    padding-right: 2rem;
}
@keyframes ticker {
    0% { transform: translateX(0); }
    100% { transform: translateX(-100%); }
}

</style>

<!-- Ultra-Enhanced Quiz Demo JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Update current time
    function updateTime() {
        const now = new Date();
        const timeString = now.toLocaleTimeString('bn-BD', { hour: '2-digit', minute: '2-digit' });
        document.getElementById('currentTime').textContent = timeString;
    }
    updateTime();
    setInterval(updateTime, 1000);
    
    const quizQuestions = [
        {
            question: "বাংলাদেশের রাজধানী কোনটি?",
            options: ["চট্টগ্রাম", "ঢাকা", "সিলেট", "রাজশাহী"],
            correct: 1,
            reward: { money: 50, coins: 25 }
        },
        {
            question: "বাংলাদেশের জাতীয় ফুল কোনটি?",
            options: ["গোলাপ", "শাপলা", "জুঁই", "বেলি"],
            correct: 1,
            reward: { money: 50, coins: 25 }
        },
        {
            question: "বাংলাদেশের মুদ্রার নাম কি?",
            options: ["রুপি", "ডলার", "টাকা", "পাউন্ড"],
            correct: 2,
            reward: { money: 50, coins: 25 }
        },
        {
            question: "বাংলাদেশের জাতীয় কবি কে?",
            options: ["রবীন্দ্রনাথ ঠাকুর", "কাজী নজরুল ইসলাম", "জীবনানন্দ দাশ", "মাইকেল মধুসূদন দত্ত"],
            correct: 1,
            reward: { money: 50, coins: 25 }
        },
        {
            question: "বাংলাদেশের স্বাধীনতা দিবস কবে?",
            options: ["২১ ফেব্রুয়ারি", "১৬ ডিসেম্বর", "২৬ মার্চ", "১৪ এপ্রিল"],
            correct: 2,
            reward: { money: 50, coins: 25 }
        }
    ];
    
    let currentQuestion = 0;
    let currentBalance = 1250;
    let currentCoins = 850;
    let timer;
    let timeLeft = 15;
    
    const startBtn = document.getElementById('startQuizDemo');
    const nextBtn = document.getElementById('nextQuestion');
    const questionText = document.getElementById('questionText');
    const questionNumber = document.getElementById('questionNumber');
    const timerElement = document.getElementById('timer');
    const balanceAmount = document.getElementById('balanceAmount');
    const coinAmount = document.getElementById('coinAmount');
    const progressBar = document.getElementById('progressBar');
    const progressText = document.getElementById('progressText');
    const earningAnimation = document.getElementById('earningAnimation');
    const quizOptions = document.querySelectorAll('.quiz-option');
    
    function startTimer() {
        timeLeft = 15;
        timerElement.parentElement.classList.remove('from-red-50', 'to-red-100');
        timerElement.parentElement.classList.add('from-orange-50', 'to-red-50');
        timerElement.classList.remove('text-red-600');
        timerElement.classList.add('text-orange-600');
        
        timer = setInterval(() => {
            timeLeft--;
            timerElement.textContent = timeLeft;
            
            if (timeLeft <= 5) {
                timerElement.parentElement.classList.remove('from-orange-50', 'to-red-50');
                timerElement.parentElement.classList.add('from-red-50', 'to-red-100');
                timerElement.classList.remove('text-orange-600');
                timerElement.classList.add('text-red-600');
                
                // Add pulse animation to timer
                timerElement.classList.add('animate-pulse');
            } else {
                timerElement.classList.remove('animate-pulse');
            }
            
            if (timeLeft <= 0) {
                clearInterval(timer);
                handleTimeUp();
            }
        }, 1000);
    }
    
    function handleTimeUp() {
        quizOptions.forEach(option => {
            option.disabled = true;
            if (option.dataset.answer !== 'correct') {
                option.classList.add('wrong');
            }
        });
        setTimeout(() => {
            nextQuestion();
        }, 2000);
    }
    
    function loadQuestion() {
        if (currentQuestion >= quizQuestions.length) {
            showCompletionMessage();
            return;
        }
        
        const question = quizQuestions[currentQuestion];
        questionText.textContent = question.question;
        questionNumber.textContent = currentQuestion + 1;
        
        quizOptions.forEach((option, index) => {
            option.innerHTML = `<span class="font-black mr-3 text-xl text-gray-600 group-hover:text-blue-600">${String.fromCharCode(65 + index)}.</span> <span class="text-gray-700 group-hover:text-blue-800 text-lg">${question.options[index]}</span>`;
            option.disabled = false;
            option.className = 'quiz-option w-full text-left p-5 rounded-2xl border-2 border-gray-200 hover:border-blue-400 hover:bg-blue-50 transition-all group';
            option.dataset.answer = index === question.correct ? 'correct' : 'wrong';
        });
        
        // Update progress with animation
        const progress = ((currentQuestion + 1) / quizQuestions.length) * 100;
        progressBar.style.width = progress + '%';
        progressText.textContent = Math.round(progress) + '%';
        
        earningAnimation.style.display = 'none';
        nextBtn.style.display = 'none';
        startTimer();
    }
    
    function handleAnswer(selectedOption) {
        clearInterval(timer);
        quizOptions.forEach(option => {
            option.disabled = true;
            if (option.dataset.answer === 'correct') {
                option.classList.add('correct');
            } else if (option === selectedOption && option.dataset.answer === 'wrong') {
                option.classList.add('wrong');
            }
        });
        
        if (selectedOption.dataset.answer === 'correct') {
            // Show earning animation
            const reward = quizQuestions[currentQuestion].reward;
            currentBalance += reward.money;
            currentCoins += reward.coins;
            
            // Animate balance increase with counting effect
            animateValue(balanceAmount, parseInt(balanceAmount.textContent.replace(/,/g, '')), currentBalance, 1000);
            animateValue(coinAmount, parseInt(coinAmount.textContent.replace(/,/g, '')), currentCoins, 1000);
            
            earningAnimation.style.display = 'block';
        }
        
        setTimeout(() => {
            nextBtn.style.display = 'inline-block';
        }, 1500);
    }
    
    function animateValue(element, start, end, duration) {
        const range = end - start;
        const increment = range / (duration / 16);
        let current = start;
        
        const timer = setInterval(() => {
            current += increment;
            if ((increment > 0 && current >= end) || (increment < 0 && current <= end)) {
                element.textContent = end.toLocaleString();
                clearInterval(timer);
            } else {
                element.textContent = Math.floor(current).toLocaleString();
            }
        }, 16);
    }
    
    function nextQuestion() {
        currentQuestion++;
        if (currentQuestion < quizQuestions.length) {
            loadQuestion();
        } else {
            showCompletionMessage();
        }
    }
    
    function showCompletionMessage() {
        const quizContainer = document.querySelector('.quiz-question');
        const totalEarned = currentBalance - 1250;
        const totalCoins = currentCoins - 850;
        
        quizContainer.innerHTML = `
            <div class="text-center py-8">
                <div class="mb-8">
                    <div class="inline-flex items-center justify-center w-32 h-32 rounded-full bg-gradient-to-r from-yellow-400 to-orange-500 mb-6 animate-pulse">
                        <i class="fas fa-trophy text-white text-6xl"></i>
                    </div>
                    <h5 class="text-green-600 font-black text-3xl mb-4">কুইজ সম্পন্ন হয়েছে! 🎉</h5>
                    <p class="text-gray-600 mb-8 text-xl">আপনি সফলভাবে সকল কুইজ সম্পন্ন করেছেন!</p>
                </div>
                <div class="flex justify-center gap-8 mb-10">
                    <div class="bg-gradient-to-br from-green-50 to-emerald-50 border-2 border-green-200 text-green-800 p-8 rounded-2xl text-center shadow-xl transform hover:scale-105 transition-transform">
                        <h6 class="font-semibold text-xl mb-3">মোট উপার্জন</h6>
                        <div class="flex items-center justify-center">
                            <span class="text-4xl font-black">৳${totalEarned.toLocaleString()}</span>
                            <span class="ml-3 text-green-500 animate-bounce">▲</span>
                        </div>
                    </div>
                    <div class="bg-gradient-to-br from-yellow-50 to-orange-50 border-2 border-yellow-200 text-yellow-800 p-8 rounded-2xl text-center shadow-xl transform hover:scale-105 transition-transform">
                        <h6 class="font-semibold text-xl mb-3">বোনাস কয়েন</h6>
                        <div class="flex items-center justify-center">
                            <span class="text-4xl font-black">${totalCoins.toLocaleString()}</span>
                            <i class="fas fa-coins text-yellow-500 ml-3 text-2xl"></i>
                        </div>
                    </div>
                </div>
                <div class="flex justify-center gap-6">
                    <button onclick="restartDemo()" class="group relative overflow-hidden bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white font-bold py-4 px-10 rounded-2xl transition-all transform hover:scale-105 hover:shadow-xl text-xl">
                        <span class="relative z-10 flex items-center">
                            <i class="fas fa-redo mr-3 group-hover:animate-spin"></i>
                            <span>আবার খেলুন</span>
                        </span>
                    </button>
                    <a href="/register" class="group relative overflow-hidden bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white font-bold py-4 px-10 rounded-2xl transition-all transform hover:scale-105 hover:shadow-xl text-xl">
                        <span class="relative z-10 flex items-center">
                            <i class="fas fa-rocket mr-3 group-hover:animate-bounce"></i>
                            <span>এখনই যোগ দিন</span>
                        </span>
                    </a>
                </div>
            </div>
        `;
        
        nextBtn.style.display = 'none';
        earningAnimation.style.display = 'none';
    }
    
    window.restartDemo = function() {
        currentQuestion = 0;
        currentBalance = 1250;
        currentCoins = 850;
        balanceAmount.textContent = currentBalance.toLocaleString();
        coinAmount.textContent = currentCoins.toLocaleString();
        location.reload();
    }
    
    // Event listeners
    startBtn.addEventListener('click', function() {
        this.style.display = 'none';
        loadQuestion();
    });
    
    nextBtn.addEventListener('click', nextQuestion);
    
    quizOptions.forEach(option => {
        option.addEventListener('click', function() {
            if (!this.disabled) {
                handleAnswer(this);
            }
        });
    });
});
</script>
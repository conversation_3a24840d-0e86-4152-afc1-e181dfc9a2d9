-- JobSpace Database Schema - Notifications & Advertising System
-- Comprehensive notification and advertising platform

-- =====================================================
-- NOTIFICATIONS SYSTEM
-- =====================================================

-- Notification templates
CREATE TABLE notification_templates (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    type ENUM('email', 'push', 'sms', 'in_app') NOT NULL,
    subject VARCHAR(500) NULL,
    content TEXT NOT NULL,
    variables JSON NULL, -- available template variables
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_name (name),
    INDEX idx_type (type),
    INDEX idx_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- User notifications
CREATE TABLE notifications (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    uuid CHAR(36) UNIQUE NOT NULL,
    user_id BIGINT UNSIGNED NOT NULL,
    type ENUM('quiz_result', 'job_application', 'contract_update', 'payment', 'social', 'system', 'marketing') NOT NULL,
    title VARCHAR(500) NOT NULL,
    message TEXT NOT NULL,
    action_url VARCHAR(1000) NULL,
    action_text VARCHAR(100) NULL,
    icon VARCHAR(255) NULL,
    image VARCHAR(500) NULL,
    priority ENUM('low', 'normal', 'high', 'urgent') DEFAULT 'normal',
    channel ENUM('in_app', 'email', 'push', 'sms') DEFAULT 'in_app',
    is_read BOOLEAN DEFAULT FALSE,
    read_at TIMESTAMP NULL,
    data JSON NULL, -- additional notification data
    expires_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_type (type),
    INDEX idx_priority (priority),
    INDEX idx_channel (channel),
    INDEX idx_is_read (is_read),
    INDEX idx_created_at (created_at),
    INDEX idx_expires_at (expires_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
PARTITION BY RANGE (UNIX_TIMESTAMP(created_at)) (
    PARTITION p_current VALUES LESS THAN (UNIX_TIMESTAMP('2025-01-01')),
    PARTITION p_2025 VALUES LESS THAN (UNIX_TIMESTAMP('2026-01-01')),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);

-- Email queue for batch processing
CREATE TABLE email_queue (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    to_email VARCHAR(255) NOT NULL,
    to_name VARCHAR(255) NULL,
    subject VARCHAR(500) NOT NULL,
    body TEXT NOT NULL,
    template_id BIGINT UNSIGNED NULL,
    priority ENUM('low', 'normal', 'high') DEFAULT 'normal',
    status ENUM('pending', 'processing', 'sent', 'failed', 'cancelled') DEFAULT 'pending',
    attempts INT UNSIGNED DEFAULT 0,
    max_attempts INT UNSIGNED DEFAULT 3,
    scheduled_at TIMESTAMP NULL,
    sent_at TIMESTAMP NULL,
    failed_at TIMESTAMP NULL,
    error_message TEXT NULL,
    metadata JSON NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (template_id) REFERENCES notification_templates(id) ON DELETE SET NULL,
    INDEX idx_to_email (to_email),
    INDEX idx_status (status),
    INDEX idx_priority (priority),
    INDEX idx_scheduled_at (scheduled_at),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- ADVERTISING SYSTEM
-- =====================================================

-- Ad campaigns
CREATE TABLE ad_campaigns (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    uuid CHAR(36) UNIQUE NOT NULL,
    advertiser_id BIGINT UNSIGNED NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT NULL,
    campaign_type ENUM('banner', 'video', 'native', 'sponsored_post', 'quiz_promotion', 'job_promotion') NOT NULL,
    status ENUM('draft', 'pending_approval', 'active', 'paused', 'completed', 'cancelled', 'rejected') DEFAULT 'draft',
    budget_type ENUM('daily', 'total', 'unlimited') DEFAULT 'total',
    daily_budget DECIMAL(12,2) NULL,
    total_budget DECIMAL(12,2) NULL,
    spent_amount DECIMAL(12,2) DEFAULT 0.00,
    bid_strategy ENUM('cpc', 'cpm', 'cpa', 'fixed') DEFAULT 'cpc',
    bid_amount DECIMAL(8,4) NOT NULL,
    target_audience JSON NULL, -- demographics, interests, location
    placement_types JSON NOT NULL, -- where ads can appear
    start_date TIMESTAMP NOT NULL,
    end_date TIMESTAMP NULL,
    impressions INT UNSIGNED DEFAULT 0,
    clicks INT UNSIGNED DEFAULT 0,
    conversions INT UNSIGNED DEFAULT 0,
    ctr DECIMAL(5,4) DEFAULT 0.0000,
    cpc DECIMAL(8,4) DEFAULT 0.0000,
    cpm DECIMAL(8,4) DEFAULT 0.0000,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (advertiser_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_advertiser_id (advertiser_id),
    INDEX idx_campaign_type (campaign_type),
    INDEX idx_status (status),
    INDEX idx_budget_type (budget_type),
    INDEX idx_dates (start_date, end_date),
    INDEX idx_performance (ctr, cpc),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Ad creatives
CREATE TABLE ad_creatives (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    campaign_id BIGINT UNSIGNED NOT NULL,
    name VARCHAR(255) NOT NULL,
    creative_type ENUM('image', 'video', 'carousel', 'text', 'html') NOT NULL,
    title VARCHAR(255) NULL,
    description TEXT NULL,
    call_to_action VARCHAR(100) NULL,
    destination_url VARCHAR(1000) NOT NULL,
    image_url VARCHAR(500) NULL,
    video_url VARCHAR(500) NULL,
    thumbnail_url VARCHAR(500) NULL,
    html_content TEXT NULL,
    dimensions JSON NULL, -- width, height
    file_size INT UNSIGNED NULL,
    duration INT UNSIGNED NULL, -- for videos
    is_active BOOLEAN DEFAULT TRUE,
    impressions INT UNSIGNED DEFAULT 0,
    clicks INT UNSIGNED DEFAULT 0,
    ctr DECIMAL(5,4) DEFAULT 0.0000,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (campaign_id) REFERENCES ad_campaigns(id) ON DELETE CASCADE,
    INDEX idx_campaign_id (campaign_id),
    INDEX idx_creative_type (creative_type),
    INDEX idx_active (is_active),
    INDEX idx_performance (ctr, clicks),
    FULLTEXT idx_content_search (title, description)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Ad placements and zones
CREATE TABLE ad_placements (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    zone_type ENUM('header', 'sidebar', 'footer', 'content', 'popup', 'interstitial', 'native') NOT NULL,
    page_types JSON NOT NULL, -- home, quiz, job, product, etc.
    dimensions JSON NOT NULL, -- width, height
    max_file_size INT UNSIGNED NULL,
    allowed_formats JSON NULL, -- image, video, html
    base_price DECIMAL(8,4) NOT NULL, -- base CPM/CPC price
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_zone_type (zone_type),
    INDEX idx_active (is_active),
    INDEX idx_base_price (base_price)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Ad impressions tracking
CREATE TABLE ad_impressions (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    campaign_id BIGINT UNSIGNED NOT NULL,
    creative_id BIGINT UNSIGNED NOT NULL,
    placement_id BIGINT UNSIGNED NOT NULL,
    user_id BIGINT UNSIGNED NULL,
    session_id VARCHAR(255) NULL,
    ip_address VARCHAR(45) NOT NULL,
    user_agent TEXT NULL,
    page_url VARCHAR(1000) NOT NULL,
    referrer_url VARCHAR(1000) NULL,
    device_type VARCHAR(50) NULL,
    browser VARCHAR(100) NULL,
    os VARCHAR(100) NULL,
    country VARCHAR(100) NULL,
    city VARCHAR(100) NULL,
    cost DECIMAL(8,4) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (campaign_id) REFERENCES ad_campaigns(id) ON DELETE CASCADE,
    FOREIGN KEY (creative_id) REFERENCES ad_creatives(id) ON DELETE CASCADE,
    FOREIGN KEY (placement_id) REFERENCES ad_placements(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_campaign_id (campaign_id),
    INDEX idx_creative_id (creative_id),
    INDEX idx_placement_id (placement_id),
    INDEX idx_user_id (user_id),
    INDEX idx_ip_address (ip_address),
    INDEX idx_created_at (created_at),
    INDEX idx_cost (cost)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
PARTITION BY RANGE (UNIX_TIMESTAMP(created_at)) (
    PARTITION p_current VALUES LESS THAN (UNIX_TIMESTAMP('2025-01-01')),
    PARTITION p_2025 VALUES LESS THAN (UNIX_TIMESTAMP('2026-01-01')),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);

-- Ad clicks tracking
CREATE TABLE ad_clicks (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    impression_id BIGINT UNSIGNED NULL,
    campaign_id BIGINT UNSIGNED NOT NULL,
    creative_id BIGINT UNSIGNED NOT NULL,
    placement_id BIGINT UNSIGNED NOT NULL,
    user_id BIGINT UNSIGNED NULL,
    session_id VARCHAR(255) NULL,
    ip_address VARCHAR(45) NOT NULL,
    user_agent TEXT NULL,
    click_url VARCHAR(1000) NOT NULL,
    destination_url VARCHAR(1000) NOT NULL,
    device_type VARCHAR(50) NULL,
    browser VARCHAR(100) NULL,
    os VARCHAR(100) NULL,
    country VARCHAR(100) NULL,
    city VARCHAR(100) NULL,
    cost DECIMAL(8,4) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (impression_id) REFERENCES ad_impressions(id) ON DELETE SET NULL,
    FOREIGN KEY (campaign_id) REFERENCES ad_campaigns(id) ON DELETE CASCADE,
    FOREIGN KEY (creative_id) REFERENCES ad_creatives(id) ON DELETE CASCADE,
    FOREIGN KEY (placement_id) REFERENCES ad_placements(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_impression_id (impression_id),
    INDEX idx_campaign_id (campaign_id),
    INDEX idx_creative_id (creative_id),
    INDEX idx_placement_id (placement_id),
    INDEX idx_user_id (user_id),
    INDEX idx_created_at (created_at),
    INDEX idx_cost (cost)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
PARTITION BY RANGE (UNIX_TIMESTAMP(created_at)) (
    PARTITION p_current VALUES LESS THAN (UNIX_TIMESTAMP('2025-01-01')),
    PARTITION p_2025 VALUES LESS THAN (UNIX_TIMESTAMP('2026-01-01')),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);

-- Ad conversions tracking
CREATE TABLE ad_conversions (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    click_id BIGINT UNSIGNED NULL,
    campaign_id BIGINT UNSIGNED NOT NULL,
    user_id BIGINT UNSIGNED NULL,
    conversion_type ENUM('signup', 'quiz_purchase', 'job_application', 'product_purchase', 'subscription') NOT NULL,
    conversion_value DECIMAL(12,2) NULL,
    revenue DECIMAL(12,2) NULL,
    commission DECIMAL(12,2) NULL,
    reference_type VARCHAR(50) NULL,
    reference_id BIGINT UNSIGNED NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (click_id) REFERENCES ad_clicks(id) ON DELETE SET NULL,
    FOREIGN KEY (campaign_id) REFERENCES ad_campaigns(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_click_id (click_id),
    INDEX idx_campaign_id (campaign_id),
    INDEX idx_user_id (user_id),
    INDEX idx_conversion_type (conversion_type),
    INDEX idx_reference (reference_type, reference_id),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

<?php

namespace App\Modules\Public\Models;

/**
 * Navigation Model
 * Handles navigation data operations
 */
class NavigationModel
{
    /**
     * Get navigation configuration
     */
    public static function getNavigationConfig(): array
    {
        return include BASE_PATH . '/app/modules/public/config/navigation.php';
    }

    /**
     * Get main menu items
     */
    public static function getMainMenu(): array
    {
        $config = self::getNavigationConfig();
        return $config['main_menu'] ?? [];
    }

    /**
     * Get footer links
     */
    public static function getFooterLinks(): array
    {
        $config = self::getNavigationConfig();
        return $config['footer_links'] ?? [];
    }

    /**
     * Get breadcrumb settings
     */
    public static function getBreadcrumbSettings(): array
    {
        $config = self::getNavigationConfig();
        return $config['breadcrumb_settings'] ?? [
            'enabled' => true,
            'separator' => '/',
            'home_text' => 'Home'
        ];
    }

    /**
     * Find navigation item by URL
     */
    public static function findByUrl(string $url): ?array
    {
        $mainMenu = self::getMainMenu();
        
        foreach ($mainMenu as $item) {
            if ($item['url'] === $url) {
                return $item;
            }
        }
        
        return null;
    }

    /**
     * Get navigation items by order
     */
    public static function getOrderedMainMenu(): array
    {
        $menu = self::getMainMenu();
        
        usort($menu, function($a, $b) {
            return ($a['order'] ?? 0) <=> ($b['order'] ?? 0);
        });
        
        return $menu;
    }

    /**
     * Check if URL exists in navigation
     */
    public static function urlExists(string $url): bool
    {
        return self::findByUrl($url) !== null;
    }

    /**
     * Get navigation item title by URL
     */
    public static function getTitleByUrl(string $url): ?string
    {
        $item = self::findByUrl($url);
        return $item['title'] ?? null;
    }

    /**
     * Get navigation item icon by URL
     */
    public static function getIconByUrl(string $url): ?string
    {
        $item = self::findByUrl($url);
        return $item['icon'] ?? null;
    }
}

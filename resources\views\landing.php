<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JobSpace - মডিউলার প্ল্যাটফর্ম</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .card-hover {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="bg-purple-600 text-white py-4 shadow-md">
        <div class="container mx-auto px-4 flex justify-between items-center">
            <div class="flex items-center space-x-2">
                <div class="w-10 h-10 bg-white rounded-lg flex items-center justify-center">
                    <span class="text-purple-600 font-bold text-xl">J</span>
                </div>
                <h1 class="text-2xl font-bold">JobSpace</h1>
            </div>
            <nav class="hidden md:flex space-x-6">
                <a href="#features" class="hover:text-purple-200 transition">ফিচার</a>
                <a href="#about" class="hover:text-purple-200 transition">সম্পর্কে</a>
                <a href="#contact" class="hover:text-purple-200 transition">যোগাযোগ</a>
            </nav>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="gradient-bg text-white py-20">
        <div class="container mx-auto px-4 text-center">
            <h2 class="text-5xl font-bold mb-6">স্বাগতম JobSpace এ</h2>
            <p class="text-xl mb-8 max-w-2xl mx-auto">
                একটি সম্পূর্ণ মডিউলার প্ল্যাটফর্ম যেখানে ফ্রিল্যান্সিং, সোশ্যাল মিডিয়া, ই-কমার্স এবং কুইজ সিস্টেম একসাথে।
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <button class="bg-white text-purple-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition">
                    শুরু করুন
                </button>
                <button class="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-purple-600 transition">
                    আরও জানুন
                </button>
            </div>
        </div>
    </section>

    <!-- Status Section -->
    <section class="py-16 bg-white">
        <div class="container mx-auto px-4">
            <div class="text-center mb-12">
                <h3 class="text-3xl font-bold text-gray-800 mb-4">সিস্টেম স্ট্যাটাস</h3>
                <p class="text-gray-600">বর্তমান সিস্টেমের অবস্থা</p>
            </div>
            
            <div class="grid md:grid-cols-3 gap-8">
                <div class="text-center p-6 bg-green-50 rounded-lg card-hover">
                    <div class="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                    </div>
                    <h4 class="text-xl font-semibold text-gray-800 mb-2">কোর সিস্টেম</h4>
                    <p class="text-green-600 font-medium">✅ সক্রিয়</p>
                    <p class="text-sm text-gray-600 mt-2">মূল সিস্টেম সফলভাবে চালু</p>
                </div>
                <div class="text-center p-6 bg-blue-50 rounded-lg card-hover">
                    <div class="w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                        </svg>
                    </div>
                    <h4 class="text-xl font-semibold text-gray-800 mb-2">মডিউলার সিস্টেম</h4>
                    <p class="text-blue-600 font-medium">🔧 প্রস্তুত</p>
                    <p class="text-sm text-gray-600 mt-2">প্লাগ এন্ড প্লে আর্কিটেকচার</p>
                </div>
                <div class="text-center p-6 bg-yellow-50 rounded-lg card-hover">
                    <div class="w-16 h-16 bg-yellow-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"></path>
                        </svg>
                    </div>
                    <h4 class="text-xl font-semibold text-gray-800 mb-2">মডিউল</h4>
                    <p class="text-yellow-600 font-medium">⏳ অপেক্ষমান</p>
                    <p class="text-sm text-gray-600 mt-2">কোনো মডিউল ইনস্টল করা নেই</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="py-16 bg-gray-50">
        <div class="container mx-auto px-4">
            <div class="text-center mb-12">
                <h3 class="text-3xl font-bold text-gray-800 mb-4">আসছে শীঘ্রই</h3>
                <p class="text-gray-600">যে সব ফিচার আসছে</p>
            </div>
            
            <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div class="bg-white p-6 rounded-lg shadow-md card-hover">
                    <div class="w-12 h-12 bg-purple-500 rounded-lg flex items-center justify-center mb-4">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6a2 2 0 01-2 2H8a2 2 0 01-2-2V8a2 2 0 012-2V6"></path>
                        </svg>
                    </div>
                    <h4 class="text-lg font-semibold mb-2">ফ্রিল্যান্সিং</h4>
                    <p class="text-gray-600 text-sm">কাজ খোঁজা এবং দেওয়ার প্ল্যাটফর্ম</p>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-md card-hover">
                    <div class="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center mb-4">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                        </svg>
                    </div>
                    <h4 class="text-lg font-semibold mb-2">সোশ্যাল মিডিয়া</h4>
                    <p class="text-gray-600 text-sm">ফেসবুকের মতো সোশ্যাল ফিড</p>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-md card-hover">
                    <div class="w-12 h-12 bg-green-500 rounded-lg flex items-center justify-center mb-4">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                        </svg>
                    </div>
                    <h4 class="text-lg font-semibold mb-2">ই-কমার্স</h4>
                    <p class="text-gray-600 text-sm">অনলাইন শপিং প্ল্যাটফর্ম</p>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-md card-hover">
                    <div class="w-12 h-12 bg-red-500 rounded-lg flex items-center justify-center mb-4">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                        </svg>
                    </div>
                    <h4 class="text-lg font-semibold mb-2">কুইজ সিস্টেম</h4>
                    <p class="text-gray-600 text-sm">ইন্টারেক্টিভ কুইজ প্ল্যাটফর্ম</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Platform Setup Section -->
    <section class="py-16 bg-white">
        <div class="container mx-auto px-4 max-w-4xl">
            <div class="bg-white rounded-2xl shadow-xl p-8">
                <!-- Header -->
                <div class="text-center mb-8">
                    <div class="text-6xl mb-4">🚀</div>
                    <h1 class="text-4xl font-bold text-gray-800 mb-2">JobSpace</h1>
                    <p class="text-xl text-gray-600">High-Performance Modular Platform</p>
                </div>
                
                <!-- Status -->
                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6 mb-8">
                    <div class="flex items-center mb-4">
                        <div class="text-2xl mr-3">⚠️</div>
                        <h2 class="text-lg font-semibold text-yellow-800">Platform Setup Required</h2>
                    </div>
                    <p class="text-yellow-700 mb-4">
                        No modules are currently installed. This is the default landing page that appears when
                        the Public module or other modules are not available.
                    </p>
                    <div class="bg-yellow-100 rounded p-3">
                        <p class="text-sm text-yellow-800">
                            <strong>To activate the platform:</strong> Install the Public module or other modules
                            in the <code class="bg-yellow-200 px-1 rounded">app/modules/</code> directory.
                        </p>
                    </div>
                </div>
                
                <!-- Platform Info -->
                <div class="grid md:grid-cols-2 gap-6 mb-8">
                    <div class="bg-blue-50 rounded-lg p-6">
                        <h3 class="text-lg font-semibold text-blue-800 mb-3">🎯 Platform Features</h3>
                        <ul class="text-blue-700 space-y-2 text-sm">
                            <li>• Modular Architecture</li>
                            <li>• High Performance (50K+ users)</li>
                            <li>• Multiple Systems Integration</li>
                            <li>• Scalable Design</li>
                        </ul>
                    </div>
                    <div class="bg-green-50 rounded-lg p-6">
                        <h3 class="text-lg font-semibold text-green-800 mb-3">📦 Available Modules</h3>
                        <ul class="text-green-700 space-y-2 text-sm">
                            <li>• Public Pages Module</li>
                            <li>• Quiz System Module</li>
                            <li>• Social Media Module</li>
                            <li>• E-commerce Module</li>
                            <li>• Freelancing Module</li>
                        </ul>
                    </div>
                </div>
                
                <!-- System Info -->
                <div class="bg-gray-50 rounded-lg p-6 mb-8">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">🔧 System Information</h3>
                    <div class="grid md:grid-cols-2 gap-4 text-sm">
                        <div>
                            <span class="font-medium text-gray-700">PHP Version:</span>
                            <span class="text-gray-600">8.1.0</span>
                        </div>
                        <div>
                            <span class="font-medium text-gray-700">Framework:</span>
                            <span class="text-gray-600">JobSpace Custom</span>
                        </div>
                        <div>
                            <span class="font-medium text-gray-700">Memory Usage:</span>
                            <span class="text-gray-600">2.45 MB</span>
                        </div>
                        <div>
                            <span class="font-medium text-gray-700">Load Time:</span>
                            <span class="text-gray-600">45.32 ms</span>
                        </div>
                    </div>
                </div>
                
                <!-- Actions -->
                <div class="text-center space-y-4">
                    <div class="text-sm text-gray-500">
                        Once modules are installed, this page will automatically redirect to the main platform.
                    </div>
                    <div class="flex justify-center space-x-4">
                        <a href="/jobspace/test"
                           class="bg-blue-500 text-white px-6 py-2 rounded-lg hover:bg-blue-600 transition">
                            🧪 Test System
                        </a>
                        <a href="/jobspace/api/status"
                           class="bg-green-500 text-white px-6 py-2 rounded-lg hover:bg-green-600 transition">
                            📊 API Status
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-8">
        <div class="container mx-auto px-4 text-center">
            <div class="flex items-center justify-center space-x-2 mb-4">
                <div class="w-8 h-8 bg-purple-600 rounded-lg flex items-center justify-center">
                    <span class="text-white font-bold">J</span>
                </div>
                <span class="text-xl font-bold">JobSpace</span>
            </div>
            <p class="text-gray-400 mb-4">মডিউলার আর্কিটেকচার সহ সম্পূর্ণ ওয়েব প্ল্যাটফর্ম</p>
            <div class="text-sm text-gray-500">
                <p>&copy; 2024 JobSpace. সকল অধিকার সংরক্ষিত।</p>
                <p class="mt-2">Pure PHP + Tailwind CSS + MariaDB</p>
            </div>
        </div>
    </footer>
</body>
</html>
<?php

require_once __DIR__ . '/../../bootstrap/app.php';

use App\Core\Database;

try {
    $db = Database::getInstance();
    
    // Demo users data
    $demoUsers = [
        [
            'first_name' => 'Admin',
            'last_name' => 'User',
            'email' => '<EMAIL>',
            'password' => password_hash('admin123', PASSWORD_DEFAULT),
            'role' => 'admin',
            'status' => 'active',
            'email_verified_at' => date('Y-m-d H:i:s'),
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ],
        [
            'first_name' => 'Creator',
            'last_name' => 'User',
            'email' => '<EMAIL>',
            'password' => password_hash('creator123', PASSWORD_DEFAULT),
            'role' => 'creator',
            'status' => 'active',
            'email_verified_at' => date('Y-m-d H:i:s'),
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ],
        [
            'first_name' => 'Regular',
            'last_name' => 'User',
            'email' => '<EMAIL>',
            'password' => password_hash('user123', PASSWORD_DEFAULT),
            'role' => 'user',
            'status' => 'active',
            'email_verified_at' => date('Y-m-d H:i:s'),
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ]
    ];
    
    // Check if users already exist
    foreach ($demoUsers as $userData) {
        $existingUser = $db->selectOne(
            "SELECT id FROM users WHERE email = :email",
            ['email' => $userData['email']]
        );
        
        if (!$existingUser) {
            $db->insert('users', $userData);
            echo "Created demo user: {$userData['email']}\n";
        } else {
            echo "Demo user already exists: {$userData['email']}\n";
        }
    }
    
    echo "\nDemo users seeding completed successfully!\n";
    echo "\nDemo Login Credentials:\n";
    echo "Admin: <EMAIL> / admin123\n";
    echo "Creator: <EMAIL> / creator123\n";
    echo "User: <EMAIL> / user123\n";
    
} catch (Exception $e) {
    echo "Error seeding demo users: " . $e->getMessage() . "\n";
}

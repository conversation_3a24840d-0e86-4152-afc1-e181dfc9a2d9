<?php

/**
 * Dashboard Routes
 * Web routes for dashboard module
 */

use App\Modules\Dashboard\Controllers\DashboardController;

$controller = new DashboardController();

// Dashboard routes
return [
    // Main dashboard (auto-redirect based on role)
    '/dashboard' => [$controller, 'index'],
    
    // Role-specific dashboards
    '/dashboard/admin' => [$controller, 'admin'],
    '/dashboard/creator' => [$controller, 'creator'],
    '/dashboard/business' => [$controller, 'business'],
    '/dashboard/user' => [$controller, 'user'],
    
    // API endpoints
    '/dashboard/api/data' => [$controller, 'getDashboardData'],
    '/dashboard/api/widgets/settings' => [$controller, 'updateWidgetSettings'],
];

<?php

namespace App\Modules\Auth\Services;

use App\Modules\Auth\Models\UserModel;

/**
 * Registration Service
 * Handles user registration process
 */
class RegistrationService
{
    private AuthService $authService;

    public function __construct()
    {
        $this->authService = new AuthService();
    }

    /**
     * Create new user account
     */
    public function createUser(array $data): array
    {
        try {
            // Validate required fields
            $requiredFields = ['first_name', 'last_name', 'email', 'username', 'phone', 'password', 'role'];
            foreach ($requiredFields as $field) {
                if (empty($data[$field])) {
                    return [
                        'success' => false,
                        'message' => "Required field missing: {$field}"
                    ];
                }
            }

            // Check if email already exists
            if (UserModel::emailExists($data['email'])) {
                return [
                    'success' => false,
                    'message' => 'এই ইমেইল দিয়ে ইতিমধ্যে একটি অ্যাকাউন্ট রয়েছে'
                ];
            }

            // Check if username already exists
            if (UserModel::usernameExists($data['username'])) {
                return [
                    'success' => false,
                    'message' => 'এই ইউজারনেম ইতিমধ্যে ব্যবহৃত হয়েছে'
                ];
            }

            // Check if phone already exists
            if (UserModel::phoneExists($data['phone'])) {
                return [
                    'success' => false,
                    'message' => 'এই মোবাইল নম্বর দিয়ে ইতিমধ্যে একটি অ্যাকাউন্ট রয়েছে'
                ];
            }

            // Hash password
            $data['password_hash'] = $this->authService->hashPassword($data['password']);
            unset($data['password'], $data['confirm_password']);

            // Handle profile picture upload
            if (isset($_FILES['profile_picture']) && $_FILES['profile_picture']['error'] === UPLOAD_ERR_OK) {
                $uploadResult = $this->handleProfilePictureUpload($_FILES['profile_picture']);
                if ($uploadResult['success']) {
                    $data['profile_picture'] = $uploadResult['filename'];
                }
            }

            // Generate referral code for new user
            $data['my_referral_code'] = $this->generateReferralCode();

            // Create user
            $result = UserModel::create($data);

            if ($result['success']) {
                // Log registration
                $this->logRegistration($result['user']);

                return [
                    'success' => true,
                    'message' => 'অ্যাকাউন্ট সফলভাবে তৈরি হয়েছে',
                    'user' => $result['user']
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'অ্যাকাউন্ট তৈরি করতে সমস্যা হয়েছে'
                ];
            }

        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'একটি ত্রুটি ঘটেছে। আবার চেষ্টা করুন।'
            ];
        }
    }

    /**
     * Handle profile picture upload
     */
    private function handleProfilePictureUpload(array $file): array
    {
        $allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
        $maxSize = 2 * 1024 * 1024; // 2MB

        // Validate file type
        if (!in_array($file['type'], $allowedTypes)) {
            return [
                'success' => false,
                'message' => 'শুধুমাত্র JPG, PNG এবং GIF ফাইল আপলোড করা যাবে'
            ];
        }

        // Validate file size
        if ($file['size'] > $maxSize) {
            return [
                'success' => false,
                'message' => 'ফাইলের আকার ২MB এর কম হতে হবে'
            ];
        }

        // Create upload directory if not exists
        $uploadDir = BASE_PATH . '/public/uploads/profiles/';
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }

        // Generate unique filename
        $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
        $filename = uniqid('profile_') . '.' . $extension;
        $filepath = $uploadDir . $filename;

        // Move uploaded file
        if (move_uploaded_file($file['tmp_name'], $filepath)) {
            return [
                'success' => true,
                'filename' => $filename
            ];
        } else {
            return [
                'success' => false,
                'message' => 'ফাইল আপলোড করতে সমস্যা হয়েছে'
            ];
        }
    }

    /**
     * Generate unique referral code
     */
    private function generateReferralCode(): string
    {
        do {
            $code = strtoupper(substr(md5(uniqid(rand(), true)), 0, 8));
        } while (UserModel::findByReferralCode($code));

        return $code;
    }

    /**
     * Log registration activity
     */
    private function logRegistration(array $user): void
    {
        // In real application, this would log to database or file
        error_log("New user registered: {$user['email']} (ID: {$user['id']})");
    }

    /**
     * Send welcome email
     */
    public function sendWelcomeEmail(array $user): bool
    {
        // In real application, this would send actual email
        // For now, just return true
        return true;
    }

    /**
     * Send email verification
     */
    public function sendEmailVerification(array $user): bool
    {
        // In real application, this would send verification email
        // For now, just return true
        return true;
    }

    /**
     * Verify email with token
     */
    public function verifyEmail(string $token): array
    {
        // In real application, this would verify the token and update user
        return [
            'success' => true,
            'message' => 'ইমেইল সফলভাবে যাচাই হয়েছে'
        ];
    }

    /**
     * Check if registration is enabled
     */
    public function isRegistrationEnabled(): bool
    {
        return $this->authService->getConfig('registration.enabled', true);
    }

    /**
     * Get registration statistics
     */
    public function getRegistrationStats(): array
    {
        return [
            'total_users' => UserModel::count(),
            'users_today' => UserModel::countToday(),
            'users_this_month' => UserModel::countThisMonth(),
            'by_role' => [
                'admin' => UserModel::countByRole('admin'),
                'creator' => UserModel::countByRole('creator'),
                'business' => UserModel::countByRole('business'),
                'user' => UserModel::countByRole('user')
            ]
        ];
    }
}

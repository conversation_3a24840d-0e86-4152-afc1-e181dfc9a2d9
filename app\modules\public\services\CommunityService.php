<?php

namespace App\Modules\Public\Services;

/**
 * Community Service
 * Handles community-related functionality
 */
class CommunityService
{
    /**
     * Get community statistics
     */
    public function getCommunityStats(): array
    {
        return [
            [
                'value' => '25,000+',
                'label' => 'Active Members',
                'icon' => 'fas fa-users',
                'color' => 'blue',
                'description' => 'সক্রিয় সদস্য'
            ],
            [
                'value' => '150,000+',
                'label' => 'Posts Created',
                'icon' => 'fas fa-edit',
                'color' => 'green',
                'description' => 'তৈরি পোস্ট'
            ],
            [
                'value' => '500,000+',
                'label' => 'Interactions',
                'icon' => 'fas fa-heart',
                'color' => 'red',
                'description' => 'ইন্টারঅ্যাকশন'
            ],
            [
                'value' => '98%',
                'label' => 'Satisfaction Rate',
                'icon' => 'fas fa-star',
                'color' => 'yellow',
                'description' => 'সন্তুষ্টির হার'
            ]
        ];
    }

    /**
     * Get recent community activities
     */
    public function getRecentActivities(): array
    {
        return [
            [
                'user' => 'রহিম আহমেদ',
                'action' => 'একটি নতুন কুইজ তৈরি করেছেন',
                'time' => '২ ঘন্টা আগে',
                'type' => 'quiz',
                'avatar' => '/jobspace/public/assets/images/avatars/default.png'
            ],
            [
                'user' => 'ফাতেমা বেগম',
                'action' => 'একটি পোস্ট শেয়ার করেছেন',
                'time' => '৪ ঘন্টা আগে',
                'type' => 'social',
                'avatar' => '/jobspace/public/assets/images/avatars/default.png'
            ],
            [
                'user' => 'করিম উদ্দিন',
                'action' => 'একটি প্রোডাক্ট লিস্ট করেছেন',
                'time' => '৬ ঘন্টা আগে',
                'type' => 'ecommerce',
                'avatar' => '/jobspace/public/assets/images/avatars/default.png'
            ],
            [
                'user' => 'সাজিদ হাসান',
                'action' => 'একটি জব পোস্ট করেছেন',
                'time' => '৮ ঘন্টা আগে',
                'type' => 'freelance',
                'avatar' => '/jobspace/public/assets/images/avatars/default.png'
            ],
            [
                'user' => 'নাদিয়া খান',
                'action' => 'একটি কুইজে অংশগ্রহণ করেছেন',
                'time' => '১০ ঘন্টা আগে',
                'type' => 'quiz',
                'avatar' => '/jobspace/public/assets/images/avatars/default.png'
            ]
        ];
    }

    /**
     * Get community features
     */
    public function getCommunityFeatures(): array
    {
        return [
            [
                'title' => 'সোশ্যাল নেটওয়ার্কিং',
                'description' => 'অন্যদের সাথে যুক্ত হন এবং আপনার অভিজ্ঞতা শেয়ার করুন',
                'icon' => 'fas fa-users',
                'color' => 'blue'
            ],
            [
                'title' => 'লিডারবোর্ড',
                'description' => 'টপ পারফরমারদের সাথে প্রতিযোগিতা করুন',
                'icon' => 'fas fa-trophy',
                'color' => 'green'
            ],
            [
                'title' => 'আলোচনা ফোরাম',
                'description' => 'বিভিন্ন বিষয়ে আলোচনায় অংশগ্রহণ করুন',
                'icon' => 'fas fa-comments',
                'color' => 'purple'
            ],
            [
                'title' => 'পুরস্কার ও ইভেন্ট',
                'description' => 'বিশেষ ইভেন্ট এবং পুরস্কারে অংশগ্রহণ করুন',
                'icon' => 'fas fa-gift',
                'color' => 'yellow'
            ]
        ];
    }

    /**
     * Get activity type icon
     */
    public function getActivityTypeIcon(string $type): string
    {
        $icons = [
            'quiz' => 'fas fa-brain',
            'social' => 'fas fa-share',
            'ecommerce' => 'fas fa-shopping-cart',
            'freelance' => 'fas fa-briefcase',
            'default' => 'fas fa-circle'
        ];

        return $icons[$type] ?? $icons['default'];
    }

    /**
     * Get activity type color
     */
    public function getActivityTypeColor(string $type): string
    {
        $colors = [
            'quiz' => 'blue',
            'social' => 'green',
            'ecommerce' => 'purple',
            'freelance' => 'orange',
            'default' => 'gray'
        ];

        return $colors[$type] ?? $colors['default'];
    }

    /**
     * Format activity time
     */
    public function formatActivityTime(string $time): string
    {
        // This is a simple implementation
        // In real application, you would use proper date formatting
        return $time;
    }
}

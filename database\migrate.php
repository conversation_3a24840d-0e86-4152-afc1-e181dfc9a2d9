<?php

require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../app/core/Database.php';

use App\Core\Database;

try {
    $db = Database::getInstance();

    // Disable foreign key checks temporarily
    echo "Disabling foreign key checks...\n";
    $db->getConnection()->exec("SET FOREIGN_KEY_CHECKS = 0");

    // Read and execute migration
    $migrationFile = __DIR__ . '/../app/modules/auth/migrations/001_create_users_table.sql';

    if (!file_exists($migrationFile)) {
        die("Migration file not found: $migrationFile\n");
    }

    $sql = file_get_contents($migrationFile);

    // Split by semicolon and execute each statement
    $statements = array_filter(array_map('trim', explode(';', $sql)));

    foreach ($statements as $statement) {
        if (!empty($statement)) {
            echo "Executing: " . substr($statement, 0, 50) . "...\n";
            $db->getConnection()->exec($statement);
        }
    }

    // Re-enable foreign key checks
    echo "Re-enabling foreign key checks...\n";
    $db->getConnection()->exec("SET FOREIGN_KEY_CHECKS = 1");

    echo "Migration completed successfully!\n";
    
} catch (Exception $e) {
    echo "Migration failed: " . $e->getMessage() . "\n";
    exit(1);
}

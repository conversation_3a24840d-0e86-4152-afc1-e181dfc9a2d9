<?php

namespace App\Modules\Auth\Models;

/**
 * User Model
 * Handles user data operations
 */
class UserModel
{
    /**
     * Find user by ID
     */
    public static function findById(int $id): ?array
    {
        // In real application, this would query the database
        // For now, returning mock data
        $users = self::getMockUsers();
        
        foreach ($users as $user) {
            if ($user['id'] === $id) {
                return $user;
            }
        }
        
        return null;
    }

    /**
     * Find user by email
     */
    public static function findByEmail(string $email): ?array
    {
        $users = self::getMockUsers();
        
        foreach ($users as $user) {
            if ($user['email'] === $email) {
                return $user;
            }
        }
        
        return null;
    }

    /**
     * Find user by username
     */
    public static function findByUsername(string $username): ?array
    {
        $users = self::getMockUsers();
        
        foreach ($users as $user) {
            if ($user['username'] === $username) {
                return $user;
            }
        }
        
        return null;
    }

    /**
     * Find user by email or username
     */
    public static function findByEmailOrUsername(string $identifier): ?array
    {
        $user = self::findByEmail($identifier);
        if ($user) {
            return $user;
        }
        
        return self::findByUsername($identifier);
    }

    /**
     * Check if email exists
     */
    public static function emailExists(string $email): bool
    {
        return self::findByEmail($email) !== null;
    }

    /**
     * Check if username exists
     */
    public static function usernameExists(string $username): bool
    {
        return self::findByUsername($username) !== null;
    }

    /**
     * Check if phone exists
     */
    public static function phoneExists(string $phone): bool
    {
        $users = self::getMockUsers();
        
        foreach ($users as $user) {
            if ($user['phone'] === $phone) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * Count users by role
     */
    public static function countByRole(string $role): int
    {
        $users = self::getMockUsers();
        $count = 0;
        
        foreach ($users as $user) {
            if ($user['role'] === $role) {
                $count++;
            }
        }
        
        return $count;
    }

    /**
     * Create new user
     */
    public static function create(array $data): array
    {
        // In real application, this would insert into database
        // For now, simulating user creation
        
        $user = [
            'id' => rand(1000, 9999), // Mock ID
            'first_name' => $data['first_name'],
            'last_name' => $data['last_name'],
            'email' => $data['email'],
            'username' => $data['username'],
            'phone' => $data['phone'],
            'date_of_birth' => $data['date_of_birth'],
            'gender' => $data['gender'],
            'address' => $data['address'] ?? '',
            'role' => $data['role'],
            'password_hash' => $data['password_hash'],
            'profile_picture' => $data['profile_picture'] ?? null,
            'referral_code' => $data['referral_code'] ?? null,
            'email_verified' => false,
            'is_active' => true,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        return [
            'success' => true,
            'user' => $user
        ];
    }

    /**
     * Update user
     */
    public static function update(int $id, array $data): bool
    {
        // In real application, this would update database
        return true;
    }

    /**
     * Delete user
     */
    public static function delete(int $id): bool
    {
        // In real application, this would delete from database
        return true;
    }

    /**
     * Get user profile
     */
    public static function getProfile(int $id): ?array
    {
        $user = self::findById($id);
        
        if (!$user) {
            return null;
        }

        // Remove sensitive data
        unset($user['password_hash']);
        
        return $user;
    }

    /**
     * Update last login
     */
    public static function updateLastLogin(int $id): bool
    {
        // In real application, this would update last_login timestamp
        return true;
    }

    /**
     * Find user by referral code
     */
    public static function findByReferralCode(string $code): ?array
    {
        $users = self::getMockUsers();

        foreach ($users as $user) {
            if (isset($user['my_referral_code']) && $user['my_referral_code'] === $code) {
                return $user;
            }
        }

        return null;
    }

    /**
     * Count total users
     */
    public static function count(): int
    {
        return count(self::getMockUsers());
    }

    /**
     * Count users registered today
     */
    public static function countToday(): int
    {
        // In real application, this would query database
        return 0;
    }

    /**
     * Count users registered this month
     */
    public static function countThisMonth(): int
    {
        // In real application, this would query database
        return 0;
    }

    /**
     * Mock users data (for development)
     */
    private static function getMockUsers(): array
    {
        return [
            [
                'id' => 1,
                'first_name' => 'Admin',
                'last_name' => 'User',
                'email' => '<EMAIL>',
                'username' => 'admin',
                'phone' => '01700000000',
                'date_of_birth' => '1990-01-01',
                'gender' => 'male',
                'address' => 'Dhaka, Bangladesh',
                'role' => 'admin',
                'password_hash' => password_hash('admin123', PASSWORD_ARGON2ID),
                'profile_picture' => null,
                'referral_code' => null,
                'my_referral_code' => 'ADMIN001',
                'email_verified' => true,
                'is_active' => true,
                'created_at' => '2024-01-01 00:00:00',
                'updated_at' => '2024-01-01 00:00:00'
            ]
        ];
    }
}
